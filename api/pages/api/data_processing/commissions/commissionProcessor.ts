import { Prisma, type statement_data } from '@prisma/client';
import * as Sentry from '@sentry/nextjs';
import BigNumber from 'bignumber.js';
import { AccountIds, MATH_MAGIC_NUMBERS } from 'common/constants';
import { TransactionStatuses } from 'common/globalTypes';
import { generateAgentPayoutRate, numberOrDefault } from 'common/helpers';
import type { CustomMethodConfig } from 'common/interface';
import dayjs from 'dayjs';
import { inject, injectable } from 'inversify';
import cloneDeep from 'lodash-es/cloneDeep';
import isEqual from 'lodash-es/isEqual';
import { nanoid } from 'nanoid';
import pRetry from 'p-retry';

import {
  type AncestorApplicableProfile,
  getApplicableCompProfiles,
  getContactAncestorsWithCommissionProfilesCTE,
} from '@/lib/commissions';
import { UnsupportedCalculationParallelLevelException } from '@/lib/exceptionHandler';
import { limitConcurrency } from '@/lib/helpers';
import { prismaClient } from '@/lib/prisma';
import { CommissionCalculationProfileService } from '@/services/commission-calculation/comp-profile';
import { prismaTransactionHandler } from '@/lib/prisma/utils';
import { TimerStats } from '@/lib/timerStats';
import { CommissionRetrieval } from '@/services/commission-calculation/commissionRetrieval';
import {
  DataProcessingStatuses,
  DataProcessingTypes,
  DataStates,
  type ExtNextApiRequest,
  type ExtNextApiResponse,
} from '@/types';
import {
  type AgentCommissionCalcLogItem,
  CommissionCalcContext,
} from './commissionCalContext';
import { CommissionCalculator } from './commissionCalculator';
import { CommissionUtils } from './commissionUtils';
import { COMMISSION_STATUS_EDITABLE, RULE_FLAGS } from './commissions.constant';
import { FeatureFlagsService } from '@/services/featureFlag';

export const policyScopedAccounts = [
  'dZCHFCKbh-YBMxKjZKfkS',
  'tY4K6TGT8NH6yMREQf2XQ',
  'EoSqZ1CPSt0GX56Eqt-Sx',
  '8LKborwusmH-8C79qmpfx',
  'iUjjVw3DmYXVPQdKIX5TC',
];

@injectable()
export class CommissionProcessor {
  @inject(CommissionUtils)
  // @ts-expect-error
  private readonly commissionUtils: CommissionUtils;

  @inject(CommissionRetrieval)
  // @ts-expect-error
  private readonly commissionRetrieval: CommissionRetrieval;

  @inject(CommissionCalculator)
  // @ts-expect-error
  private readonly commissionCalculator: CommissionCalculator;

  @inject(TimerStats)
  // @ts-expect-error
  private readonly timerStats: TimerStats;

  @inject(CommissionCalculationProfileService)
  // @ts-expect-error
  private readonly commissionCalcProfileService: CommissionCalculationProfileService;

  @inject(FeatureFlagsService)
  // @ts-expect-error
  private readonly featureFlagsService: FeatureFlagsService;

  processCommissions = async (
    req: ExtNextApiRequest,
    res: ExtNextApiResponse
  ) => {
    const ctx = CommissionCalcContext.init(req.body);
    const isContactV2 = await this.featureFlagsService.isFeatureEnabled({
      feature: 'contactsV2',
      accountId: req.account_id as string,
    });

    // biome-ignore lint/suspicious/noImplicitAnyLet: Suppressed in initial biome linting check -- please fix these errors if you are modifying this file --INITIAL_FIX--
    let dataProcessing;
    const startTime = Date.now();
    try {
      console.time('calculateAgentCommissions()');
      const {
        id,
        ids,
        useGroupedCommissions,
        contactIds,
        documentIds,
        startDate: _startDate,
        endDate: _endDate,
        payingEntity,
        policyId,
        statementIds,
        onlyGetProfilesRates,
        regressionTestMode,
        regressionAccount,
      } = req.body;
      const startDate = _startDate
        ? dayjs.utc(_startDate).startOf('day').toDate()
        : null;
      const endDate = _endDate
        ? dayjs.utc(_endDate).endOf('day').toDate()
        : null;
      console.time('getAllData()');
      const { lookupData, statementData, useCompGrids } =
        await this.commissionRetrieval.getAllData({
          account_id: ctx.regressionTestMode
            ? ctx.regressionAccount
            : req.account_id,
          contactIds,
          documentIds,
          startDate,
          endDate,
          payingEntity,
          policyId,
          statementIds,
          id,
          ids,
          regressionTestMode,
          useGroupedCommissions,
          regressionAccount,
        });
      console.timeEnd('getAllData()');

      const params = Object.fromEntries(
        Object.entries({
          documentIds,
          startDate,
          endDate,
          payingEntity,
          useGroupedCommissions,
          policyId,
          statementIds,
          id,
          ids,
          // biome-ignore lint/correctness/noUnusedFunctionParameters: Suppressed in initial biome linting check -- please fix these errors if you are modifying this file --INITIAL_FIX--
        }).filter(([k, v]) => ![undefined, null, ''].includes(v))
      );

      if (!onlyGetProfilesRates || !ctx.regressionTestMode) {
        // @ts-expect-error
        dataProcessing = await prismaClient.data_processing.create({
          data: {
            str_id: nanoid(),
            account: { connect: { str_id: req.account_id } },
            user: { connect: { uid: req.uid } },
            proxy_user: req.ouid ? { connect: { uid: req.ouid } } : undefined,
            master: req.body.master_str_id
              ? { connect: { str_id: req.body.master_str_id } }
              : undefined,
            type: DataProcessingTypes.agent_commission_calc,
            params: JSON.stringify(params),
            status: DataProcessingStatuses.PROCESSING,
          },
        });
      }

      let cacheHits = 0;

      // For every commission statement
      // 1. Get the applicable commission schedules for the agents on the record
      // 2. Calculate the commission for each agent
      // 3. Update the commission statement with the calculated commission
      console.time('Get contacts');
      const contacts = isContactV2
        ? await this.commissionRetrieval.fetchContactsFromStatements(
            statementData
          )
        : await this.commissionRetrieval.getContactsV1({
            accountId: req.account_id as string,
            regressionTestMode: ctx.regressionTestMode,
          });

      console.timeEnd('Get contacts');

      const contactsMap: Record<string | number, (typeof contacts)[0]> = {};
      for (const contact of contacts) {
        contactsMap[contact.id] = contact;
        if (contact.str_id) contactsMap[contact.str_id] = contact;
      }

      let processedCountNew = 0;
      // let processedCount = 0;

      // For policy scope, we limit the number of parallel calculations to 1
      const calculationParallelLevel = policyScopedAccounts.includes(
        req.account_id as string
      )
        ? 1
        : 60;

      let errorOfPolicyScopeLevel = false;

      // @ts-expect-error
      const taskHandlerNew = async (statement) => {
        const taskLogger = req.logger.getChildLogger({
          statementId: statement.id,
        });
        taskLogger.profile('taskHandlerNew');
        req.logger.info(
          `${(Date.now() - startTime) / 1000 / (processedCountNew / statementData.length)}: ${processedCountNew} / ${statementData.length}`
        );
        if (useGroupedCommissions && statement.state === DataStates.GROUPED) {
          taskLogger.info(
            `Skipping grouped commission statement: ${statement.str_id} because useGroupedCommissions is true`
          );
          return;
        }
        taskLogger.info(
          `Processing statement: ${statement.str_id} (${processedCountNew + 1}/${statementData.length})`
        );
        const startStatementTime = Date.now();
        let commissionsUsed = BigNumber(0);
        // let directProducerRate;
        const directProducerRateMap = {};
        const adjustedRatesMap = {};
        const hierarchySplitPercentageMap = {};
        const effectiveDate =
          statement.effective_date ?? statement.report?.effective_date;

        const extraAgentStrIds: string[] = [];
        const profiles = [];
        let runningTotal = BigNumber(0);
        for await (const statementContactStrId of statement.contacts) {
          const key = `${statementContactStrId}-${effectiveDate}`;
          if (!ctx.ancestryProfilesMap.has(key)) {
            // TODO: What do we do if not everyone in the lineage has an applicable schedule/rule?
            const ancestryProfiles =
              await getContactAncestorsWithCommissionProfilesCTE({
                strId: statementContactStrId,
                effectiveDate,
                req: { account_id: req.account_id as string },
                commissionCalcProfileService: this.commissionCalcProfileService,
                cache: ctx.profileCache,
                timerStats: this.timerStats,
              });

            if (!ancestryProfiles) {
              // biome-ignore lint/suspicious/noConsole: Suppressed in initial biome linting check -- please fix these errors if you are modifying this file --INITIAL_FIX--
              console.error('Could not find agent');
              ctx.errors.push(
                `${statement.str_id}: Could not find agent: ${statementContactStrId}`
              );
              continue;
            }
            ctx.ancestryProfilesMap.set(key, ancestryProfiles);
            profiles.push(...ancestryProfiles);
          } else {
            taskLogger.info(`Using cached profiles for ${key}`);
            cacheHits++;
          }
        }
        const agentsToFetch = this.commissionUtils.determineExtraAgentsToFetch(
          statement,
          profiles
        );

        if (agentsToFetch.length > 0) {
          extraAgentStrIds.push(...agentsToFetch);
        }
        const statementContactsSorted = [
          ...extraAgentStrIds,
          ...statement.contacts,
        ].sort((a, b) => {
          if (
            this.commissionUtils.isIMO(contactsMap[a]) &&
            this.commissionUtils.isIMO(contactsMap[b])
          )
            return 0;
          if (this.commissionUtils.isIMO(contactsMap[a])) return -1;
          if (this.commissionUtils.isIMO(contactsMap[b])) return 1;
          if (
            this.commissionUtils.isSalesRep(contactsMap[a]) &&
            this.commissionUtils.isSalesRep(contactsMap[b])
          )
            return 0;
          if (this.commissionUtils.isSalesRep(contactsMap[a])) return 1;
          if (this.commissionUtils.isSalesRep(contactsMap[b])) return -1;
          return 0;
        });
        for await (const statementContactStrId of statementContactsSorted) {
          taskLogger.info(`Processing contact: ${statementContactStrId}`);
          const key = `${statementContactStrId}-${effectiveDate}`;
          // biome-ignore lint/suspicious/noExplicitAny: Suppressed in initial biome linting check -- please fix these errors if you are modifying this file --INITIAL_FIX--
          let ancestryProfiles = ctx.ancestryProfilesMap.get<any[]>(key) ?? [];
          // If the contact is outside of the hiearchy, only get the very first profile of it
          if (
            ancestryProfiles.length &&
            extraAgentStrIds.includes(statementContactStrId)
          ) {
            ancestryProfiles = [ancestryProfiles[0]];
          }

          // biome-ignore lint/complexity/noForEach: Suppressed in initial biome linting check -- please fix these errors if you are modifying this file --INITIAL_FIX--
          Object.values(RULE_FLAGS).forEach((rule) => {
            this.commissionUtils.addFlagIfConditionMet(statement, ctx, rule);
          });

          // ??? Do we need all uplines regardless of whether assigned agent has anything that applies?
          // TODO: Warn if everyone doesn't have an applicable profile
          let ancestryApplicableProfiles: AncestorApplicableProfile[] = [];
          const startContactTime = Date.now();
          // Contacts have multiple comp profiles, each profile can have multiple rules
          taskLogger.profile('getApplicableCompProfiles');
          const ancestryApplicableProfilesAll = (
            await limitConcurrency(
              async (profile) => {
                if (
                  AccountIds.ALEVO === req.account_id &&
                  !this.commissionUtils.filterApplicableProfile(
                    profile,
                    statement
                  )
                ) {
                  return null;
                }

                const agentCommissionProfile = await getApplicableCompProfiles(
                  {
                    compProfiles: profile.commissionProfiles,
                    agentStrId: profile.agentStrId,
                    statementContactStrId,
                    agent: contactsMap[profile.agentStrId],
                  },
                  statement,
                  lookupData,
                  effectiveDate,
                  useCompGrids,
                  ctx.agentCommissionCalcLog,
                  null,
                  ctx.compGridCache,
                  this.timerStats
                );
                return agentCommissionProfile
                  ? {
                      agentId: profile.agentId,
                      agentStrId: profile.agentStrId,
                      agentCommissionProfile,
                      hierarchySplitPercentage:
                        profile.hierarchySplitPercentage,
                    }
                  : null;
              },
              ancestryProfiles ?? [],
              30,
              {
                retries: 0,
                onFail: (failedTask) => {
                  // biome-ignore lint/suspicious/noConsole: Suppressed in initial biome linting check -- please fix these errors if you are modifying this file --INITIAL_FIX--
                  console.error(
                    `Task failed for profile: ${JSON.stringify(failedTask)}`
                  );
                },
              }
            )
          ).filter(Boolean);
          taskLogger.profile('getApplicableCompProfiles');
          taskLogger.profile('findProfilesWithNoHierarchyProcessing');
          const updatedAncestryApplicableProfilesAll =
            this.commissionUtils.findProfilesWithNoHierarchyProcessing(
              ancestryApplicableProfilesAll,
              ancestryProfiles,
              statement?.contacts ?? []
            );

          taskLogger.profile('findProfilesWithNoHierarchyProcessing');
          const endContactTime = Date.now();
          taskLogger.info(
            `Contact hierarchy with applicable profiles: ${updatedAncestryApplicableProfilesAll.length} (${endContactTime - startContactTime}ms)`
          );
          const agentApplicableProfiles = [
            updatedAncestryApplicableProfilesAll?.[0],
          ];
          const statementAgentProfile =
            updatedAncestryApplicableProfilesAll?.[0]?.agentCommissionProfile
              ?.agentProfileConfig?.agent_commission_schedule_profile;

          if (isContactV2) {
            await this.commissionRetrieval.fetchContactsFromProfiles(
              ancestryProfiles,
              contactsMap
            );
          }

          if (statementAgentProfile?.hierarchy_handling === 'none') {
            ancestryApplicableProfiles = agentApplicableProfiles;
          } else if (statementAgentProfile?.hierarchy_handling === 'upline') {
            ancestryApplicableProfiles = agentApplicableProfiles;
            if (updatedAncestryApplicableProfilesAll?.[1]) {
              ancestryApplicableProfiles.push(
                updatedAncestryApplicableProfilesAll[1]
              );
            }
          } else {
            ancestryApplicableProfiles = updatedAncestryApplicableProfilesAll;
          }

          ancestryApplicableProfiles = ancestryApplicableProfiles.filter(
            (e) => e?.agentCommissionProfile
          );

          let previousRate = BigNumber(0);

          const effectiveSplit = this.commissionUtils.getEffectiveSplit(
            statement.report
          );

          // IMOs will often have a split of 0, but we always want to treat them as 100%
          const agentSplit = this.commissionUtils.isIMO(
            contactsMap[statementContactStrId]
          )
            ? new BigNumber(MATH_MAGIC_NUMBERS.MULTIPLY_IDENTITY)
            : ctx.getAgentSplitFromEffectiveSplit(
                effectiveSplit,
                statementContactStrId
              );
          let commissionAmountSplitted = BigNumber(
            +statement.commission_amount
          ).times(agentSplit);
          const defaultBasis = [
            'annual_premium',
            'target_premium',
            'normalized_premium',
            'premium',
          ].includes(statementAgentProfile?.calculation_basis)
            ? BigNumber(+statement.premium_amount)
            : BigNumber(+statement.commission_amount);

          // @ts-expect-error
          let actualCalcBasis: BigNumber = null;
          const calcBasis = this.commissionCalculator.getCalcBasis({
            defaultBasis,
            statement,
            calculation_basis: statementAgentProfile?.calculation_basis,
            agentSplit,
          });

          let calculationBasisAmount = BigNumber(
            // @ts-expect-error
            numberOrDefault(calcBasis, defaultBasis.toNumber())
          );
          if (
            statement.commission_amount < 0 &&
            ['annual_premium', 'target_premium'].includes(
              statementAgentProfile?.calculation_basis
            )
          ) {
            calculationBasisAmount = calculationBasisAmount.times(-1);
          }
          let calculationBasisAmountSplitted =
            calculationBasisAmount.times(agentSplit);
          if (
            statementAgentProfile?.calculation_basis === 'commissions_remaining'
          ) {
            // TODO: This might need to be on the original amount before splitting.
            commissionAmountSplitted =
              commissionAmountSplitted.minus(commissionsUsed);
            calculationBasisAmountSplitted =
              calculationBasisAmountSplitted.minus(commissionsUsed);
          }
          const commissionReceivedSign = this.commissionUtils.getValueSign(
            statementAgentProfile?.calculation_basis === 'commissions_remaining'
              ? +commissionAmountSplitted
              : +statement?.commission_amount
          );

          if (
            useGroupedCommissions &&
            statement.children_data?.length > 0 &&
            !statement.is_virtual
            // TODO: Removing requirement on no split for now (uncertain why we added it)
            // && Object.keys(statement.report?.contacts_split ?? []).length <= 1
          ) {
            // @ts-expect-error
            // biome-ignore lint/complexity/noForEach: Suppressed in initial biome linting check -- please fix these errors if you are modifying this file --INITIAL_FIX--
            statement.children_data.forEach((child) => {
              const effectiveSplit = this.commissionUtils.getEffectiveSplit(
                child.report
              );
              commissionAmountSplitted = commissionAmountSplitted.plus(
                BigNumber(+child.commission_amount)
                  .times(
                    BigNumber(
                      Number.isNaN(+effectiveSplit?.[statementContactStrId])
                        ? 100
                        : +effectiveSplit?.[statementContactStrId]
                    )
                  )
                  .div(100)
              );
            });
          }
          let statementCommissionRate = BigNumber(
            statement.new_commission_rate
          ).times(100);
          if (
            useGroupedCommissions &&
            statement.children_data?.length > 0 &&
            statement.virtual_type !== 'grouped'
            // TODO: Removing requirement on no split for now (uncertain why we added it)
            // && Object.keys(statement.report?.contacts_split ?? []).length <= 1
          ) {
            // @ts-expect-error
            // biome-ignore lint/complexity/noForEach: Suppressed in initial biome linting check -- please fix these errors if you are modifying this file --INITIAL_FIX--
            statement.children_data.forEach((child) => {
              statementCommissionRate = statementCommissionRate.plus(
                BigNumber(child.new_commission_rate).times(100)
              );
            });
          }
          let paidUplineCommissions = BigNumber(0);

          if (
            this.commissionUtils.isSalesRep(
              contactsMap[statementContactStrId]
            ) &&
            (!ancestryApplicableProfiles?.[0]?.agentCommissionProfile?.rule
              ?.calculation_method ||
              ['compGrid', 'overrideSplit'].includes(
                ancestryApplicableProfiles?.[0]?.agentCommissionProfile?.rule
                  ?.calculation_method
              ))
          ) {
            for (const {
              agentStrId,
              agentCommissionProfile,
            } of ancestryApplicableProfiles) {
              req.logger.info(`Sales rep: ${agentStrId}`);
              let payeeStrId = agentStrId;
              const rule = agentCommissionProfile.rule;
              const curCalcLog: AgentCommissionCalcLogItem = {
                agentSplit: BigNumber(agentSplit),
                multiplier:
                  +agentCommissionProfile.agentProfileConfig.multiplier / 100,
                contactStrId: statementContactStrId,
                calcMethod: rule.calculation_method,
                profile_id:
                  agentCommissionProfile.agentProfileConfig
                    .agent_commission_schedule_profile_id,
                profile_name:
                  agentCommissionProfile.agentProfileConfig
                    .agent_commission_schedule_profile.name,
                profile_str_id:
                  agentCommissionProfile.agentProfileConfig
                    .agent_commission_schedule_profile.str_id,
                agentUplines: ancestryProfiles.map((e) => e.agentName),
                rule: Object.fromEntries(
                  Object.entries(agentCommissionProfile.rule).filter(
                    // biome-ignore lint/correctness/noUnusedFunctionParameters: Suppressed in initial biome linting check -- please fix these errors if you are modifying this file --INITIAL_FIX--
                    ([k, v]) => v
                  )
                ),
              };

              const commissionConditions =
                statement.agent_payout_rate_override?.config?.[payeeStrId]
                  ?.conditions;
              const isActiveCommissionPayoutRateOverride =
                !Array.isArray(commissionConditions) ||
                (await this.commissionUtils.isActivateCustomMethod(
                  { conditions: commissionConditions } as CustomMethodConfig,
                  lookupData,
                  statement,
                  // @ts-expect-error
                  { account_id: req.account_id },
                  null,
                  useCompGrids
                ));
              const commissionPayoutRateOverride =
                isActiveCommissionPayoutRateOverride
                  ? statement.agent_payout_rate_override?.[payeeStrId]
                  : null;

              const policyConditions =
                statement.report?.agent_payout_rate_override?.config?.[
                  payeeStrId
                ]?.conditions;
              const isActivePolicyPayoutRateOverride =
                !Array.isArray(policyConditions) ||
                (await this.commissionUtils.isActivateCustomMethod(
                  { conditions: commissionConditions } as CustomMethodConfig,
                  lookupData,
                  statement,
                  // @ts-expect-error
                  { account_id: req.account_id },
                  null,
                  useCompGrids
                ));
              const policyPayoutRateOverride = isActivePolicyPayoutRateOverride
                ? statement.report?.agent_payout_rate_override?.[payeeStrId]
                : null;

              const payoutRateOverride =
                commissionPayoutRateOverride ?? policyPayoutRateOverride;

              if (
                payoutRateOverride !== null &&
                payoutRateOverride !== undefined
              ) {
                const commissionAmount = this.commissionUtils.alignSign(
                  BigNumber(payoutRateOverride)
                    .div(100)
                    .times(calculationBasisAmountSplitted)
                    .times(
                      BigNumber(
                        agentCommissionProfile?.agentProfileConfig
                          ?.multiplier ?? 100
                      ).div(100)
                    ),
                  commissionReceivedSign
                );
                ctx.addResult({
                  statementId: statement.id,
                  payeeStrId: payeeStrId,
                  amount: commissionAmount.toNumber(),
                  logs: {
                    ...curCalcLog,
                    calcBasis: calculationBasisAmountSplitted,
                    commissionAmount: commissionAmount.toNumber(),
                    // @ts-expect-error
                    payeeRates: null,
                    // @ts-expect-error
                    payerRates: null,
                    payoutRate: payoutRateOverride,
                    commissionRate: BigNumber(payoutRateOverride)
                      .div(100)
                      .toNumber(),
                    notes: 'Payout rate override',
                  },
                });
                if (
                  statement?.agent_payout_rate_override?.config?.[payeeStrId]
                    .overrideMode !== 'add'
                ) {
                  continue;
                }
              }
              if (
                rule.calculation_method === 'compGrid' ||
                !rule.calculation_method
              ) {
                const ruleMultiplier = BigNumber(+rule.multiplier || 100).div(
                  100
                );
                const commissionRate = BigNumber(rule.commission_rate)
                  .div(BigNumber(rule.receivable_rate ?? 100))
                  .times(
                    BigNumber(
                      +agentCommissionProfile?.agentProfileConfig?.multiplier ||
                        100
                    ).div(100)
                  )
                  .minus(previousRate);
                const commissionAmount = this.commissionUtils.alignSign(
                  commissionRate
                    .times(commissionAmountSplitted)
                    .times(ruleMultiplier),
                  commissionReceivedSign
                );
                runningTotal = runningTotal.plus(commissionAmount);

                ctx.addResult({
                  statementId: statement.id,
                  payeeStrId: agentStrId,
                  amount: commissionAmount,
                  logs: {
                    ...curCalcLog,
                    calcMethodMultiplier: ruleMultiplier,
                    payoutRate: commissionRate.toNumber(),
                    commissionRate: commissionRate.toNumber(),
                    commissionAmount: commissionAmount.toNumber(),
                    // biome-ignore lint/complexity/useDateNow: Suppressed in initial biome linting check -- please fix these errors if you are modifying this file --INITIAL_FIX--
                    calculatedAt: new Date().getTime(),
                  },
                });

                previousRate = previousRate.plus(commissionRate);
                taskLogger.info(
                  `Found applicable profile using 'payoutRate' method, calculating commission: (commission: ${statement.str_id}, agent: ${agentStrId}), commission_profile: ${agentCommissionProfile.agentProfileConfig.agent_commission_schedule_profile.str_id}, rule.commission_rate: ${agentCommissionProfile.rule.commission_rate}, rule.receivable_rate: ${agentCommissionProfile.rule.receivable_rate}, commissionRate: ${commissionRate}, commissionAmount: ${commissionAmount}`
                );
              } else if (rule.calculation_method === 'overrideSplit') {
                const payoutRate = BigNumber(+rule.split);
                let commissionRate = BigNumber(+rule.split)
                  .div(100)
                  .times(
                    BigNumber(
                      +agentCommissionProfile?.agentProfileConfig?.multiplier ||
                        100
                    )
                  )
                  .div(100);
                const adjustResult = await this.commissionUtils.adjustRate(
                  {
                    commissionRate,
                    agentApplicableProfile: agentCommissionProfile,
                  },
                  lookupData,
                  statement,
                  // @ts-expect-error
                  { account_id: req.account_id },
                  useCompGrids
                );
                commissionRate = adjustResult.commissionRate;

                const { finalCalcBasis, sign } =
                  this.commissionCalculator.getFinalCalcBasisWithSign({
                    defaultBasis: calculationBasisAmount,
                    runningTotal,
                    isSalesRep: true,
                    agentSplit,
                    calculationBasis:
                      agentCommissionProfile?.agentProfileConfig
                        ?.agent_commission_schedule_profile?.calculation_basis,
                    statement,
                  });
                const commissionAmount = this.commissionUtils.alignSign(
                  commissionRate
                    .times(finalCalcBasis)
                    .times(
                      BigNumber(
                        +agentCommissionProfile?.agentProfileConfig
                          ?.multiplier || 100
                      ).div(100)
                    ),
                  sign
                );
                runningTotal = runningTotal.plus(commissionAmount);
                const customResult =
                  await this.commissionCalculator.sequenceRunMethods({
                    agentApplicableProfile: agentCommissionProfile,
                    calculationBasis: calculationBasisAmountSplitted,
                    lookupData,
                    useCompGrids,
                    cache: ctx.profileCache,
                    statement,
                    // @ts-expect-error
                    contactsMap,
                    runningTotal,
                    ctx,
                    req,
                  });
                runningTotal = customResult?.runningTotal ?? runningTotal;

                if (
                  agentCommissionProfile.agentProfileConfig
                    ?.agent_commission_schedule_profile?.payee_types ===
                  'upline'
                ) {
                  const parentId =
                    contactsMap[agentStrId]?.parent_relationships?.[0]
                      ?.parent_id;
                  // @ts-expect-error
                  const agentUplineStrId = contactsMap?.[parentId]?.str_id;
                  if (!agentUplineStrId) {
                    taskLogger.warn(
                      `No upline found for agent: ${agentStrId}. Falling back to agentStrId as payeeStrId.`
                    );
                  }
                  payeeStrId = agentUplineStrId ?? agentStrId;
                }

                ctx.addResult({
                  statementId: statement.id,
                  payeeStrId: payeeStrId,
                  amount: commissionAmount,
                  logs: {
                    ...curCalcLog,
                    // biome-ignore lint/complexity/useDateNow: Suppressed in initial biome linting check -- please fix these errors if you are modifying this file --INITIAL_FIX--
                    calculatedAt: new Date().getTime(),
                    commissionRate: commissionRate,
                    commissionAmount: commissionAmount,
                    payoutRate: payoutRate,
                  },
                });

                previousRate = previousRate.plus(commissionRate);
                taskLogger.info(
                  `Found applicable profile using ${rule.calculation_method} method, calculating commission: (commission: ${statement.str_id}, agent: ${agentStrId}), commission_profile: ${agentCommissionProfile.agentProfileConfig.agent_commission_schedule_profile.str_id}, rule.commission_rate: ${agentCommissionProfile.rule.commission_rate}, rule.receivable_rate: ${agentCommissionProfile.rule.receivable_rate}, commissionRate: ${commissionRate}, commissionAmount: ${commissionAmount}`
                );
              } else {
                throw new Error(
                  "Sales reps only support 'compGrid' and 'overrideSplit' calculation methods"
                );
              }
            }
          } else {
            for await (const {
              agentStrId,
              agentCommissionProfile,
              hierarchySplitPercentage: _hierarchySplitPercentage,
            } of ancestryApplicableProfiles) {
              taskLogger.info(`Processing hierarchy, agent: ${agentStrId}`);
              let payeeStrId = agentStrId;
              if (
                agentCommissionProfile.agentProfileConfig
                  ?.agent_commission_schedule_profile?.payee_types === 'upline'
              ) {
                // const statementUplineStrId =
                //   contactsMap?.[contactsMap[statementContactStrId]?.parent_id]
                //     ?.str_id;
                const agentUplineStrId =
                  agentStrId &&
                  contactsMap[agentStrId]?.parent_relationships?.[0]?.parent_id
                    ? contactsMap?.[
                        contactsMap[agentStrId]?.parent_relationships?.[0]
                          ?.parent_id
                      ]?.str_id
                    : null;
                if (agentUplineStrId) payeeStrId = agentUplineStrId;
                else {
                  const errMsg = `Agent commission profile set to pay upline, but upline not found for statement: ${statement.str_id}, agent: ${statementContactStrId}`;
                  // biome-ignore lint/suspicious/noConsole: Suppressed in initial biome linting check -- please fix these errors if you are modifying this file --INITIAL_FIX--
                  console.error(errMsg);
                  ctx.errors.push(errMsg);
                  continue;
                }
              }

              const commissionConditions =
                statement.agent_payout_rate_override?.config?.[payeeStrId]
                  ?.conditions;
              const isActiveCommissionPayoutRateOverride =
                !Array.isArray(commissionConditions) ||
                (await this.commissionUtils.isActivateCustomMethod(
                  { conditions: commissionConditions } as CustomMethodConfig,
                  lookupData,
                  statement,
                  // @ts-expect-error
                  { account_id: req.account_id },
                  null,
                  useCompGrids
                ));
              const commissionPayoutRateOverride =
                isActiveCommissionPayoutRateOverride
                  ? statement.agent_payout_rate_override?.[payeeStrId]
                  : null;

              const policyConditions =
                statement.report?.agent_payout_rate_override?.config?.[
                  payeeStrId
                ]?.conditions;
              const isActivePolicyPayoutRateOverride =
                !Array.isArray(policyConditions) ||
                (await this.commissionUtils.isActivateCustomMethod(
                  { conditions: commissionConditions } as CustomMethodConfig,
                  lookupData,
                  statement,
                  // @ts-expect-error
                  { account_id: req.account_id },
                  null,
                  useCompGrids
                ));
              const policyPayoutRateOverride = isActivePolicyPayoutRateOverride
                ? statement.report?.agent_payout_rate_override?.[payeeStrId]
                : null;

              const payoutRateOverride =
                commissionPayoutRateOverride ?? policyPayoutRateOverride;
              let isOverrideAddUsed = false;

              const payoutRateOverrideSign =
                this.commissionUtils.getValueSign(payoutRateOverride);

              const rule = agentCommissionProfile.rule;
              const curIndex = ancestryApplicableProfiles.findIndex(
                (e) => e.agentStrId === agentStrId
              );
              const downlineIndex = ancestryApplicableProfiles
                .slice(0, curIndex)
                .findLastIndex((profile) =>
                  (
                    contactsMap[agentStrId]?.child_relationships?.map(
                      (relation) => relation.contact_id
                    ) ?? []
                  ).includes(profile.agentId)
                );
              const {
                agentStrId: downlineStrId,
                agentCommissionProfile: downlineProfile,
              } = ancestryApplicableProfiles[downlineIndex] ?? {};

              const hierarchySplitPercentage = BigNumber(
                _hierarchySplitPercentage ?? 100
              )
                .div(100)
                .times(
                  BigNumber(
                    // @ts-expect-error
                    hierarchySplitPercentageMap[downlineStrId] ?? 100
                  ).div(100)
                )
                .times(100);
              // @ts-expect-error
              hierarchySplitPercentageMap[agentStrId] =
                hierarchySplitPercentage;

              let curCalcLog: AgentCommissionCalcLogItem = {
                agentSplit,
                hierarchySplit: hierarchySplitPercentage.div(100),
                multiplier: BigNumber(
                  agentCommissionProfile.agentProfileConfig.multiplier
                ).div(100),
                contactStrId: statementContactStrId,
                calcBasis: calculationBasisAmount,
                calcMethod: rule.calculation_method,
                profile_id:
                  agentCommissionProfile.agentProfileConfig
                    .agent_commission_schedule_profile_id,
                profile_name:
                  agentCommissionProfile.agentProfileConfig
                    .agent_commission_schedule_profile.name,
                profile_str_id:
                  agentCommissionProfile.agentProfileConfig
                    .agent_commission_schedule_profile.str_id,
                agentUplines: ancestryProfiles.map((e) => e.agentName),
                rule: Object.fromEntries(
                  Object.entries(agentCommissionProfile.rule).filter(
                    // biome-ignore lint/correctness/noUnusedFunctionParameters: Suppressed in initial biome linting check -- please fix these errors if you are modifying this file --INITIAL_FIX--
                    ([k, v]) => v
                  )
                ),
              };

              if (
                payoutRateOverride !== null &&
                payoutRateOverride !== undefined
              ) {
                const commissionAmount = this.commissionUtils.alignSign(
                  BigNumber(payoutRateOverride)
                    .div(100)
                    .times(calculationBasisAmountSplitted)
                    .times(
                      BigNumber(
                        agentCommissionProfile?.agentProfileConfig
                          ?.multiplier ?? 100
                      ).div(100)
                    ),
                  commissionReceivedSign,
                  payoutRateOverrideSign
                );
                ctx.addResult({
                  statementId: statement.id,
                  payeeStrId: payeeStrId,
                  amount: commissionAmount.toNumber(),
                  logs: {
                    ...curCalcLog,
                    calcBasis: actualCalcBasis || curCalcLog.calcBasis,
                    commissionAmount: commissionAmount.toNumber(),
                    // @ts-expect-error
                    payeeRates: null,
                    // @ts-expect-error
                    payerRates: null,
                    payoutRate: payoutRateOverride,
                    commissionRate: BigNumber(payoutRateOverride)
                      .div(100)
                      .toNumber(),
                    notes: 'Payout rate override',
                  },
                });
                runningTotal = runningTotal.plus(commissionAmount);
                if (
                  statement?.agent_payout_rate_override?.config?.[payeeStrId]
                    .overrideMode !== 'add'
                ) {
                  continue;
                } else {
                  isOverrideAddUsed = true;
                }
              }
              if (
                !rule.calculation_method ||
                rule.calculation_method === 'compGrid'
              ) {
                const ruleMultiplier = BigNumber(+rule.multiplier || 100).div(
                  100
                );
                if (useCompGrids) {
                  const payeeLevel =
                    agentCommissionProfile.agentProfileConfig
                      .agent_commission_schedule_profile.payee_comp_grid_level;
                  const payerLevel =
                    agentCommissionProfile.agentProfileConfig
                      .agent_commission_schedule_profile.payer_comp_grid_level;
                  if (!payeeLevel) {
                    ctx.addResult({
                      statementId: statement.id,
                      payeeStrId: payeeStrId,
                      amount: 0,
                      logs: {
                        ...curCalcLog,
                        commissionAmount: 0,
                        alerts: 'Comp grid payee level not found',
                      },
                    });
                    continue;
                  }

                  const criterionId = agentCommissionProfile.matchedCriteriaId;

                  const payerRates =
                    payerLevel?.comp_grid_rates?.findLast(
                      // @ts-expect-error
                      (rate) => rate.comp_grid_criterion_id === criterionId
                    ) ?? null;

                  // biome-ignore lint/suspicious/noImplicitAnyLet: Suppressed in initial biome linting check -- please fix these errors if you are modifying this file --INITIAL_FIX--
                  let agencyRates;
                  // biome-ignore lint/suspicious/noImplicitAnyLet: Suppressed in initial biome linting check -- please fix these errors if you are modifying this file --INITIAL_FIX--
                  let agentRates;
                  if (req.account_id === AccountIds.WORLD_CHANGERS) {
                    agencyRates = cloneDeep(
                      agentCommissionProfile.agentProfileConfig.agent_commission_schedule_profile.agency_comp_grid_level?.comp_grid_rates?.findLast(
                        // @ts-expect-error
                        (rate) => rate.comp_grid_criterion_id === criterionId
                      ) ?? null
                    );
                    agentRates = cloneDeep(
                      agentCommissionProfile.agentProfileConfig.agent_commission_schedule_profile.agent_comp_grid_level?.comp_grid_rates?.findLast(
                        // @ts-expect-error
                        (rate) => rate.comp_grid_criterion_id === criterionId
                      ) ?? null
                    );
                  }

                  const {
                    effectiveCommissionRate,
                    commissionRate,
                    payeeRates,
                    payoutRate,
                  } = await this.commissionUtils.calculateDownlineAdjustments(
                    ancestryApplicableProfiles,
                    agentStrId,
                    statementContactStrId,
                    agentCommissionProfile,
                    payeeLevel,
                    criterionId,
                    payoutRateOverride,
                    lookupData,
                    statement,
                    req,
                    useCompGrids,
                    directProducerRateMap
                  );

                  const commissionAmount = this.commissionUtils.alignSign(
                    BigNumber(calculationBasisAmountSplitted)
                      .times(effectiveCommissionRate)
                      .times(ruleMultiplier)
                      .times(hierarchySplitPercentage.div(100)),
                    commissionReceivedSign
                  );
                  const finalPayeeStrId =
                    this.commissionCalculator.payToActiveAgentInHierarchy(
                      payeeStrId,
                      ancestryProfiles,
                      contactsMap
                    );
                  runningTotal = runningTotal.plus(commissionAmount);
                  ctx.addResult({
                    statementId: statement.id,
                    // @ts-expect-error
                    payeeStrId: finalPayeeStrId,
                    amount: commissionAmount,
                    logs: {
                      ...curCalcLog,
                      calcMethodMultiplier: ruleMultiplier,
                      // biome-ignore lint/complexity/useDateNow: Suppressed in initial biome linting check -- please fix these errors if you are modifying this file --INITIAL_FIX--
                      calculatedAt: new Date().getTime(),
                      payoutRate: payoutRate.toNumber(),
                      commissionRate: commissionRate.toNumber(),
                      commissionAmount: commissionAmount.toNumber(),
                      alerts: this.commissionUtils.formatPaytoAlerts(
                        payeeStrId,
                        // @ts-expect-error
                        finalPayeeStrId,
                        contactsMap
                      ),
                      ...(payerRates !== null && { payerRates }),
                      payeeRates,
                      ...(agencyRates !== null && { agencyRates }),
                      ...(agentRates !== null && { agentRates }),
                    },
                  });
                  const customResult =
                    await this.commissionCalculator.sequenceRunMethods({
                      agentApplicableProfile: agentCommissionProfile,
                      calculationBasis: calculationBasisAmountSplitted,
                      statement,
                      lookupData,
                      useCompGrids,
                      cache: ctx.profileCache,
                      // @ts-expect-error
                      contactsMap,
                      runningTotal,
                      ctx,
                      req,
                    });
                  runningTotal = customResult?.runningTotal ?? runningTotal;

                  previousRate = previousRate.plus(commissionRate);
                  taskLogger.info(
                    `Found applicable profile using 'payoutRate' method, calculating commission: (commission: ${statement.str_id}, agent: ${agentStrId}), commission_profile: ${agentCommissionProfile.agentProfileConfig.agent_commission_schedule_profile.str_id}, rule.commission_rate: ${agentCommissionProfile.rule.commission_rate}, rule.receivable_rate: ${agentCommissionProfile.rule.receivable_rate}, commissionRate: ${commissionRate}, commissionAmount: ${commissionAmount}`
                  );
                } else {
                  const payoutRate = BigNumber(+rule.commission_rate);
                  let commissionRate = BigNumber(rule.commission_rate)
                    .div(BigNumber(rule.receivable_rate ?? 100))
                    .times(
                      BigNumber(
                        // @ts-expect-error
                        numberOrDefault(
                          agentCommissionProfile?.agentProfileConfig
                            ?.multiplier,
                          100
                        )
                      ).div(100)
                    )
                    .minus(previousRate);

                  const adjustResult = await this.commissionUtils.adjustRate(
                    {
                      commissionRate,
                      agentApplicableProfile: agentCommissionProfile,
                    },
                    lookupData,
                    statement,
                    // @ts-expect-error
                    { account_id: req.account_id },
                    useCompGrids
                  );
                  commissionRate = adjustResult.commissionRate;

                  // Req.logger.info('currentRate: ', commissionRate, previousRate);
                  const effectiveCommissionRate = BigNumber(commissionRate);
                  const commissionAmount = this.commissionUtils.alignSign(
                    effectiveCommissionRate.times(commissionAmountSplitted),
                    commissionReceivedSign
                  );
                  runningTotal = runningTotal.plus(commissionAmount);

                  const finalPayeeStrId =
                    this.commissionCalculator.payToActiveAgentInHierarchy(
                      payeeStrId,
                      ancestryProfiles,
                      contactsMap
                    );
                  ctx.agentCommissionCalc[statement.id] = {
                    ...(ctx.agentCommissionCalc[statement.id] ?? {}),
                    // @ts-expect-error
                    // biome-ignore format: ignore
                    [finalPayeeStrId]: commissionAmount.plus(
                      ctx.agentCommissionCalc[statement.id]?.[
                        // @ts-expect-error
                        // biome-ignore format: ignore
                        finalPayeeStrId
                      ] ?? 0
                    ),
                    total: commissionAmount.plus(
                      ctx.agentCommissionCalc[statement.id]?.total ?? 0
                    ),
                  };
                  const customResult =
                    await this.commissionCalculator.sequenceRunMethods({
                      agentApplicableProfile: agentCommissionProfile,
                      calculationBasis: calculationBasisAmountSplitted,
                      statement,
                      lookupData,
                      useCompGrids,
                      cache: ctx.profileCache,
                      // @ts-expect-error
                      contactsMap,
                      runningTotal,
                      ctx,
                      req,
                    });
                  runningTotal = customResult?.runningTotal ?? runningTotal;

                  ctx.agentCommissionCalcLog[statement.id] = {
                    ...(ctx.agentCommissionCalcLog[statement.id] ?? {}),
                    // @ts-expect-error
                    [finalPayeeStrId]: [
                      // biome-ignore format: ignore
                      ...
                      (
                        // biome-ignore format: ignore
                      ctx.agentCommissionCalcLog[statement.id]?.[
                        // @ts-expect-error
                        finalPayeeStrId
                      ] ?? []),
                      {
                        ...curCalcLog,
                        calcMethodMultiplier: ruleMultiplier,
                        // biome-ignore lint/complexity/useDateNow: Suppressed in initial biome linting check -- please fix these errors if you are modifying this file --INITIAL_FIX--
                        calculatedAt: new Date().getTime(),
                        payoutRate: payoutRate,
                        commissionRate: commissionRate,
                        commissionAmount: commissionAmount,
                        alerts: this.commissionUtils.formatPaytoAlerts(
                          payeeStrId,
                          // @ts-expect-error
                          finalPayeeStrId,
                          contactsMap
                        ),
                      },
                    ],
                  };
                  previousRate = previousRate.plus(effectiveCommissionRate);
                  taskLogger.info(
                    `Found applicable profile using 'payoutRate' method, calculating commission: (commission: ${statement.str_id}, agent: ${agentStrId}), commission_profile: ${agentCommissionProfile.agentProfileConfig.agent_commission_schedule_profile.str_id}, rule.commission_rate: ${agentCommissionProfile.rule.commission_rate}, rule.receivable_rate: ${agentCommissionProfile.rule.receivable_rate}, commissionRate: ${commissionRate}, commissionAmount: ${commissionAmount}`
                  );
                }
              } else if (rule.calculation_method === 'payHouseRate') {
                const payeeLevel =
                  agentCommissionProfile.agentProfileConfig
                    .agent_commission_schedule_profile.payee_comp_grid_level;
                const payerLevel =
                  agentCommissionProfile.agentProfileConfig
                    .agent_commission_schedule_profile.payer_comp_grid_level;
                const criterionId = agentCommissionProfile.matchedCriteriaId;
                const criterionStr = agentCommissionProfile.matchedCriteriaStr;
                const payeeRates = payeeLevel?.comp_grid_rates
                  ?.filter(
                    // @ts-expect-error
                    (rate) => rate.comp_grid_criterion_id === criterionId
                  )
                  // @ts-expect-error
                  ?.sort((a, b) => b.id - a.id)?.[0];
                const payerRates = payerLevel?.comp_grid_rates
                  ?.filter(
                    // @ts-expect-error
                    (rate) => rate.comp_grid_criterion_id === criterionId
                  )
                  // @ts-expect-error
                  ?.sort((a, b) => b.id - a.id)?.[0];
                const payoutRate = BigNumber(payeeRates?.house_rate ?? 0);
                let commissionRate = payoutRate.div(100).minus(previousRate);
                const adjustResult = await this.commissionUtils.adjustRate(
                  {
                    commissionRate,
                    agentApplicableProfile: agentCommissionProfile,
                  },
                  lookupData,
                  statement,
                  // @ts-expect-error
                  { account_id: req.account_id },
                  useCompGrids
                );
                commissionRate = adjustResult.commissionRate;

                const commissionAmount = this.commissionUtils.alignSign(
                  BigNumber(commissionRate)
                    .times(calculationBasisAmountSplitted)
                    .times(
                      BigNumber(
                        agentCommissionProfile?.agentProfileConfig
                          ?.multiplier ?? 100
                      ).div(100)
                    ),
                  commissionReceivedSign,
                  commissionRate.gte(0) ? 1 : -1
                );
                runningTotal = runningTotal.plus(commissionAmount);
                ctx.agentCommissionCalc[statement.id] = {
                  ...(ctx.agentCommissionCalc[statement.id] ?? {}),
                  [payeeStrId]: commissionAmount.plus(
                    ctx.agentCommissionCalc[statement.id]?.[payeeStrId] ?? 0
                  ),
                  total: commissionAmount.plus(
                    ctx.agentCommissionCalc[statement.id]?.total ?? 0
                  ),
                };
                const customResult =
                  await this.commissionCalculator.sequenceRunMethods({
                    agentApplicableProfile: agentCommissionProfile,
                    calculationBasis: calculationBasisAmountSplitted,
                    statement,
                    lookupData,
                    useCompGrids,
                    cache: ctx.profileCache,
                    // @ts-expect-error
                    contactsMap,
                    runningTotal,
                    ctx,
                    req,
                  });
                runningTotal = customResult?.runningTotal ?? runningTotal;

                ctx.agentCommissionCalcLog[statement.id] = {
                  ...(ctx.agentCommissionCalcLog[statement.id] ?? {}),
                  [payeeStrId]: [
                    ...(ctx.agentCommissionCalcLog[statement.id]?.[
                      payeeStrId
                    ] ?? []),
                    {
                      ...curCalcLog,
                      // biome-ignore lint/complexity/useDateNow: Suppressed in initial biome linting check -- please fix these errors if you are modifying this file --INITIAL_FIX--
                      calculatedAt: new Date().getTime(),
                      criterionId,
                      criterionStr,
                      commissionRate: commissionRate,
                      commissionAmount: commissionAmount,
                      payerRates,
                      payeeRates,
                      payoutRate: payoutRate,
                    },
                  ],
                };
                previousRate = previousRate.plus(commissionRate);
              } else if (
                rule.calculation_method === 'payOverrideUpToTotalRate'
              ) {
                // Writing agent is LOA / Pay commission to grid level
                const downlineLevel =
                  downlineProfile?.agentProfileConfig
                    ?.agent_commission_schedule_profile?.payee_comp_grid_level;

                const _downlineRates = downlineLevel?.comp_grid_rates?.filter(
                  // @ts-expect-error
                  (rate) =>
                    rate.comp_grid_criterion_id ===
                      downlineProfile.matchedCriteriaId &&
                    (rate.date_ranges.length === 0 ||
                      rate.date_ranges.some(
                        // @ts-expect-error
                        (range) =>
                          (!range.start_date ||
                            range.start_date <= effectiveDate) &&
                          (!range.end_date || range.end_date >= effectiveDate)
                      ))
                );
                let downlineRates = _downlineRates?.sort(
                  // @ts-expect-error
                  (a, b) => b.id - a.id
                )?.[0];
                // @ts-expect-error
                if (adjustedRatesMap[downlineStrId]) {
                  // @ts-expect-error
                  downlineRates = adjustedRatesMap[downlineStrId];
                }
                const criterionId = agentCommissionProfile.matchedCriteriaId;
                const criterionStr = agentCommissionProfile.matchedCriteriaStr;
                const payerLevel =
                  agentCommissionProfile.agentProfileConfig
                    .agent_commission_schedule_profile.payer_comp_grid_level;
                const _payerRates = payerLevel?.comp_grid_rates?.filter(
                  // @ts-expect-error
                  (rate) =>
                    rate.comp_grid_criterion_id === criterionId &&
                    (rate.date_ranges.length === 0 ||
                      rate.date_ranges.some(
                        // @ts-expect-error
                        (range) =>
                          (!range.start_date ||
                            range.start_date <= effectiveDate) &&
                          (!range.end_date || range.end_date >= effectiveDate)
                      ))
                );
                // @ts-expect-error
                const payerRates = _payerRates?.sort((a, b) => b.id - a.id)[0];
                if (_payerRates?.length > 1) {
                  req.logger.warn(
                    `Multiple payer rates found. criteriaId: ${criterionId}, level: ${payerLevel?.id}, date: ${effectiveDate?.toISOString()}`
                  );
                }
                if (!payerRates) {
                  req.logger.error(
                    `No payer rates found. criteriaId: ${criterionId}, level: ${payerLevel?.id}, date: ${effectiveDate?.toISOString()}`
                  );
                  ctx.addResult({
                    statementId: statement.id,
                    payeeStrId: payeeStrId,
                    amount: 0,
                    logs: {
                      ...curCalcLog,
                      commissionAmount: 0,
                      criterionId,
                      criterionStr,
                      alerts: 'Missing comp grid data (no payer rates found)',
                    },
                  });
                  continue;
                }
                const payeeLevel =
                  agentCommissionProfile.agentProfileConfig
                    .agent_commission_schedule_profile.payee_comp_grid_level;
                const _payeeRates = payeeLevel?.comp_grid_rates?.filter(
                  // @ts-expect-error
                  (rate) =>
                    rate.comp_grid_criterion_id === criterionId &&
                    (rate.date_ranges.length === 0 ||
                      rate.date_ranges.some(
                        // @ts-expect-error
                        (range) =>
                          (!range.start_date ||
                            range.start_date <= effectiveDate) &&
                          (!range.end_date || range.end_date >= effectiveDate)
                      ))
                );
                if (_payeeRates?.length > 1) {
                  req.logger.info(
                    `Multiple payee rates found. criteriaId: ${criterionId}, level: ${payeeLevel?.id}, date: ${effectiveDate?.toISOString()}`
                  );
                } else if (!_payeeRates || _payeeRates.length === 0) {
                  req.logger.error(
                    `No payee rates found. criteriaId: ${criterionId}, level: ${payeeLevel?.id}, date: ${effectiveDate?.toISOString()}`
                  );
                  ctx.addResult({
                    statementId: statement.id,
                    payeeStrId: payeeStrId,
                    amount: 0,
                    logs: {
                      ...curCalcLog,
                      commissionAmount: 0,
                      criterionId,
                      criterionStr,
                      alerts: 'Missing comp grid data (no payee rates found)',
                    },
                  });
                  continue;
                }
                // @ts-expect-error
                let payeeRates = _payeeRates.sort((a, b) => b.id - a.id)[0];
                // If rates are lower than downlines, use downline rates and set in adjustedRates
                if (+downlineRates?.rate > +payeeRates?.rate) {
                  // @ts-expect-error
                  adjustedRatesMap[agentStrId] = downlineRates;
                  payeeRates = downlineRates;
                }
                if (
                  // AgentCommissionProfile?.agentProfileConfig?.method === 'set' ||
                  agentCommissionProfile?.agentProfileConfig?.config?.find(
                    // @ts-expect-error
                    (r) => r.method === 'set'
                  )
                ) {
                  const adjustResult = await this.commissionUtils.adjustRate(
                    {
                      commissionRate: BigNumber(payeeRates.rate).div(100),
                      agentApplicableProfile: agentCommissionProfile,
                    },
                    lookupData,
                    statement,
                    // @ts-expect-error
                    { account_id: req.account_id },
                    useCompGrids
                  );
                  payeeRates.rate = adjustResult.commissionRate.times(100);
                }
                payeeRates.house_rate = BigNumber(payeeRates?.rate ?? 0).minus(
                  payeeRates?.carrier_rate ?? 0
                );

                // biome-ignore lint/suspicious/noImplicitAnyLet: Suppressed in initial biome linting check -- please fix these errors if you are modifying this file --INITIAL_FIX--
                let overrideRate;
                // @ts-expect-error
                if (!directProducerRateMap[agentStrId]) {
                  // @ts-expect-error
                  directProducerRateMap[agentStrId] = BigNumber(
                    payerRates?.carrier_rate ?? 0
                  )
                    .div(100)
                    .minus(statementCommissionRate.div(100));
                }

                if (
                  (payeeRates?.carrier_rate ?? 0) / 100 >
                  // @ts-expect-error
                  (directProducerRateMap[downlineStrId] ??
                    // @ts-expect-error
                    directProducerRateMap[agentStrId])
                ) {
                  overrideRate = BigNumber(payeeRates?.rate ?? 0)
                    .div(100)
                    .minus(BigNumber(downlineRates?.rate ?? 0).div(100))
                    .minus(
                      BigNumber(
                        // @ts-expect-error
                        directProducerRateMap[downlineStrId] ??
                          // @ts-expect-error
                          directProducerRateMap[agentStrId]
                      ).minus(
                        BigNumber(downlineRates?.carrier_rate ?? 0).div(100)
                      )
                    );
                  // @ts-expect-error
                  directProducerRateMap[agentStrId] = BigNumber(
                    // @ts-expect-error
                    directProducerRateMap[downlineStrId] ?? 0
                  )
                    .plus(BigNumber(payeeRates?.carrier_rate ?? 0).div(100))
                    .minus(
                      BigNumber(downlineRates?.carrier_rate ?? 0).div(100)
                    );
                } else {
                  overrideRate = BigNumber(payeeRates?.house_rate ?? 0)
                    .div(100)
                    .minus(BigNumber(downlineRates?.house_rate ?? 0).div(100));
                  // @ts-expect-error
                  directProducerRateMap[agentStrId] =
                    // @ts-expect-error
                    directProducerRateMap[downlineStrId] ??
                    // @ts-expect-error
                    directProducerRateMap[agentStrId];
                }

                let commissionRate = BigNumber(overrideRate);

                // IsOverrideAddUsed means that we'v already processed the payout override cycle, in the following cycle the addition logic should not take the payoutRateOverride into consideration

                if (payoutRateOverride && !isOverrideAddUsed) {
                  commissionRate = BigNumber(payoutRateOverride).div(100);
                }
                let isAdjusted = false;
                if (
                  // AgentCommissionProfile?.agentProfileConfig?.method === 'add' ||
                  agentCommissionProfile?.agentProfileConfig?.config?.find(
                    // @ts-expect-error
                    (r) => r.method === 'add'
                  )
                ) {
                  const adjustResult = await this.commissionUtils.adjustRate(
                    {
                      commissionRate,
                      agentApplicableProfile: agentCommissionProfile,
                    },
                    lookupData,
                    statement,
                    // @ts-expect-error
                    { account_id: req.account_id },
                    useCompGrids
                  );
                  commissionRate = adjustResult.commissionRate;
                  isAdjusted = adjustResult.isAdjusted;
                }
                const commissionAmount = this.commissionUtils.alignSign(
                  BigNumber(commissionRate)
                    .times(calculationBasisAmountSplitted)
                    .times(
                      BigNumber(
                        hierarchySplitPercentage
                          ? hierarchySplitPercentage
                          : 100
                      ).div(100)
                    )
                    .times(
                      BigNumber(
                        // @ts-expect-error
                        numberOrDefault(
                          agentCommissionProfile?.agentProfileConfig
                            ?.multiplier,
                          100
                        )
                      ).div(100)
                    ),
                  commissionReceivedSign,
                  isAdjusted
                    ? this.commissionUtils.getValueSign(
                        commissionRate.toNumber()
                      )
                    : 1
                );
                const payerCommissionRate = BigNumber(
                  BigNumber(payerRates?.carrier_rate ?? 0).minus(
                    payeeRates?.carrier_rate ?? 0
                  )
                ).div(100);
                runningTotal = runningTotal.plus(commissionAmount);
                ctx.addResult({
                  statementId: statement.id,
                  payeeStrId: payeeStrId,
                  amount: commissionAmount.toNumber(),
                  logs: {
                    ...curCalcLog,
                    commissionAmount: commissionAmount.toNumber(),
                    criterionId,
                    criterionStr,
                    payerCommissionRate: payerCommissionRate.toNumber(),
                    payerRates: payerRates,
                    payeeRates: payeeRates,
                    payoutRate: commissionRate.times(100),
                  },
                });
                previousRate = previousRate.plus(commissionRate);
                const customResult =
                  await this.commissionCalculator.sequenceRunMethods({
                    agentApplicableProfile: agentCommissionProfile,
                    calculationBasis: calculationBasisAmountSplitted,
                    statement,
                    // @ts-expect-error
                    contactsMap,
                    runningTotal,

                    lookupData,
                    useCompGrids,
                    cache: ctx.profileCache,
                    ctx,
                    req,
                    hierarchySplitPercentage,
                  });
                runningTotal = customResult?.runningTotal ?? runningTotal;
              } else if (rule.calculation_method === 'compGridLevel') {
                // if (
                //   useGroupedCommissions &&
                //   statement.children_data?.length > 0 &&
                //   Object.keys(statement.contacts_split).length <= 1
                // ) {
                //   statement.children_data.forEach((child) => {
                //     statementCommissionRate += +child.new_commission_rate * 100;
                //   });
                // }
                const ruleMultiplier = BigNumber(+rule.multiplier || 100).div(
                  100
                );
                let payoutRate = BigNumber.max(
                  BigNumber(statementCommissionRate).minus(
                    BigNumber(rule.carrier_grid_override_rate)
                  ),
                  0
                );
                // biome-ignore lint/suspicious/noImplicitAnyLet: Suppressed in initial biome linting check -- please fix these errors if you are modifying this file --INITIAL_FIX--
                let payerRates;
                // biome-ignore lint/suspicious/noImplicitAnyLet: Suppressed in initial biome linting check -- please fix these errors if you are modifying this file --INITIAL_FIX--
                let payeeRates;
                if (useCompGrids) {
                  const payerLevel =
                    agentCommissionProfile.agentProfileConfig
                      .agent_commission_schedule_profile.payer_comp_grid_level;
                  const payeeLevel =
                    agentCommissionProfile.agentProfileConfig
                      .agent_commission_schedule_profile.payee_comp_grid_level;
                  const criterionId = agentCommissionProfile.matchedCriteriaId;
                  const criterionStr =
                    agentCommissionProfile.matchedCriteriaStr;
                  payerRates = payerLevel?.comp_grid_rates
                    ?.filter(
                      // @ts-expect-error
                      (rate) => rate.comp_grid_criterion_id === criterionId
                    )
                    // @ts-expect-error
                    ?.sort((a, b) => b.id - a.id)?.[0];
                  payeeRates = payeeLevel?.comp_grid_rates
                    ?.filter(
                      // @ts-expect-error
                      (rate) => rate.comp_grid_criterion_id === criterionId
                    )
                    // @ts-expect-error
                    .sort((a, b) => b.id - a.id)?.[0];
                  if (
                    this.commissionUtils.checkInvalidRate(payerRates, [
                      'rate',
                    ]) ||
                    this.commissionUtils.checkInvalidRate(payeeRates, ['rate'])
                  ) {
                    req.logger.error(
                      `The payer or payee grid rate is missing. criteriaId: ${criterionId}, level: ${payerLevel?.id}, date: ${effectiveDate?.toISOString()}`
                    );
                    ctx.addResult({
                      statementId: statement.id,
                      payeeStrId: payeeStrId,
                      amount: 0,
                      logs: {
                        ...curCalcLog,
                        commissionAmount: 0,
                        criterionId,
                        criterionStr,
                        alerts:
                          'The compensation grid rate is missing (including null payer or payee rates, house rates, or carrier rates).',
                      },
                    });
                    continue;
                  }
                  const payerRate = BigNumber(payerRates?.rate ?? 0);
                  const payeeRate = BigNumber(payeeRates?.rate ?? 0);
                  payoutRate = BigNumber.max(
                    0,
                    BigNumber(statementCommissionRate)
                      .minus(paidUplineCommissions)
                      .minus(BigNumber(payerRate).minus(payeeRate))
                  );
                  paidUplineCommissions =
                    paidUplineCommissions.plus(payoutRate);
                }
                const isPremiumCalculationBasis =
                  agentCommissionProfile.agentProfileConfig
                    ?.agent_commission_schedule_profile?.calculation_basis ===
                  'premium';
                const { finalCalcBasis, sign } =
                  this.commissionCalculator.getFinalCalcBasisWithSign({
                    defaultBasis: calculationBasisAmount,
                    runningTotal,
                    agentSplit,
                    calculationBasis:
                      agentCommissionProfile?.agentProfileConfig
                        ?.agent_commission_schedule_profile?.calculation_basis,
                    statement,
                  });
                let commissionRate = isPremiumCalculationBasis
                  ? BigNumber(payoutRate).div(100)
                  : BigNumber(payoutRate).div(statementCommissionRate);
                const adjustResult = await this.commissionUtils.adjustRate(
                  {
                    commissionRate: commissionRate,
                    agentApplicableProfile: agentCommissionProfile,
                  },
                  lookupData,
                  statement,
                  // @ts-expect-error
                  { account_id: req.account_id },
                  useCompGrids
                );
                commissionRate = adjustResult.commissionRate;
                let commissionAmount = this.commissionUtils.alignSign(
                  commissionRate
                    .times(finalCalcBasis)
                    .times(ruleMultiplier)
                    .times(
                      BigNumber(
                        agentCommissionProfile?.agentProfileConfig
                          ?.multiplier ?? 100
                      ).div(100)
                    ),
                  sign
                );

                // TODO: Only enable for comp grids for now
                if (
                  useCompGrids &&
                  agentCommissionProfile.agentProfileConfig
                    ?.agent_commission_schedule_profile?.calculation_basis ===
                    'target_premium'
                ) {
                  commissionRate = BigNumber(payoutRate).div(100);
                  const adjustResult = await this.commissionUtils.adjustRate(
                    {
                      commissionRate: commissionRate,
                      agentApplicableProfile: agentCommissionProfile,
                    },
                    lookupData,
                    statement,
                    // @ts-expect-error
                    { account_id: req.account_id },
                    useCompGrids
                  );
                  commissionRate = adjustResult.commissionRate;
                  commissionAmount = this.commissionUtils.alignSign(
                    commissionRate
                      .times(calculationBasisAmountSplitted)
                      .times(ruleMultiplier)
                      .times(
                        BigNumber(
                          // @ts-expect-error
                          numberOrDefault(
                            agentCommissionProfile?.agentProfileConfig
                              ?.multiplier,
                            100
                          )
                        ).div(100)
                      ),
                    commissionReceivedSign
                  );
                }

                if (
                  agentCommissionProfile.agentProfileConfig
                    ?.agent_commission_schedule_profile?.calculation_scope ===
                  'policy'
                ) {
                  const result =
                    await this.commissionCalculator.policyScopeCalc({
                      accountId: req.account_id,
                      agentCommissionProfile,
                      calcBasis: calculationBasisAmountSplitted,
                      calcMethod: 'compGridLevel',
                      calculationParallelLevel,
                      commissionAmountBasis: commissionAmountSplitted,
                      contact: contactsMap[payeeStrId],
                      payoutRate: BigNumber(+payoutRate).div(100),
                      policyCommissionsSeenAmount:
                        ctx.policyCommissionsSeenAmount,
                      policyCommissionsSeenRecords:
                        ctx.policyCommissionsSeenRecords,
                      policyCommissionsUsedAmount:
                        ctx.policyCommissionsUsedAmount,
                      policyCommissionsUsedRecords:
                        ctx.policyCommissionsUsedRecords,
                      rule,
                      statement,
                    });
                  ({ commissionAmount, commissionRate, actualCalcBasis } =
                    result);
                  curCalcLog = {
                    ...curCalcLog,
                    scope: 'Policy',
                    policyCommissionsUsed: result.policyCommissionsUsed,
                  };
                }
                runningTotal = runningTotal.plus(commissionAmount);
                const customResult =
                  await this.commissionCalculator.sequenceRunMethods({
                    agentApplicableProfile: agentCommissionProfile,
                    calculationBasis: calculationBasisAmountSplitted,
                    statement,
                    // @ts-expect-error
                    contactsMap,
                    runningTotal,
                    lookupData,
                    useCompGrids,
                    cache: ctx.profileCache,
                    ctx,
                    req,
                  });
                runningTotal = customResult?.runningTotal ?? runningTotal;

                ctx.addResult({
                  statementId: statement.id,
                  payeeStrId: payeeStrId,
                  amount: commissionAmount.toNumber(),
                  logs: {
                    ...curCalcLog,
                    calcMethodMultiplier: ruleMultiplier,
                    calcBasis: actualCalcBasis || curCalcLog.calcBasis,
                    commissionAmount: commissionAmount.toNumber(),
                    payeeRates,
                    payerRates,
                    payoutRate: payoutRate.toNumber(),
                    commissionRate: commissionRate.toNumber(),
                  },
                });
                previousRate = previousRate.plus(commissionRate);
              } else if (rule.calculation_method === 'payoutRate') {
                let commissionRate = rule?.limit_to_received
                  ? BigNumber.min(
                      BigNumber(+rule.payout_rate),
                      BigNumber(statementCommissionRate || 100)
                    )
                  : BigNumber(+rule.payout_rate);
                // biome-ignore lint/suspicious/noImplicitAnyLet: Suppressed in initial biome linting check -- please fix these errors if you are modifying this file --INITIAL_FIX--
                let commissionAmount;
                // biome-ignore lint/suspicious/noImplicitAnyLet: Suppressed in initial biome linting check -- please fix these errors if you are modifying this file --INITIAL_FIX--
                let payoutRate;
                if (
                  agentCommissionProfile.agentProfileConfig
                    ?.agent_commission_schedule_profile?.calculation_scope ===
                  'policy'
                ) {
                  const result =
                    await this.commissionCalculator.policyScopeCalc({
                      accountId: req.account_id,
                      agentCommissionProfile,
                      calcBasis: calculationBasisAmountSplitted,
                      calcMethod: 'payoutRate',
                      calculationParallelLevel,
                      commissionAmountBasis: commissionAmountSplitted,
                      contact: contactsMap[payeeStrId],
                      payoutRate: BigNumber(+rule.payout_rate).div(100),
                      policyCommissionsSeenAmount:
                        ctx.policyCommissionsSeenAmount,
                      policyCommissionsSeenRecords:
                        ctx.policyCommissionsSeenRecords,
                      policyCommissionsUsedAmount:
                        ctx.policyCommissionsUsedAmount,
                      policyCommissionsUsedRecords:
                        ctx.policyCommissionsUsedAmount,
                      rule,
                      statement,
                    });
                  ({ commissionAmount, commissionRate, actualCalcBasis } =
                    result);
                  curCalcLog = {
                    ...curCalcLog,
                    policyCommissionsUsed: result.policyCommissionsUsed,
                    scope: 'Policy',
                  };
                } else if (req.account_id === AccountIds.BROKERS_ALLIANCE) {
                  // This block moves to using calculationBasisAmountSplitted, which is preferred basis
                  // Else block maintains previous behavior until we can verify / migrate all accounts to use this new method
                  // TODO: Need to figure out if we need this block for calculationBasisAmountSplitted
                  // if (
                  //   agentCommissionProfile.agentProfileConfig
                  //     ?.agent_commission_schedule_profile?.calculation_basis !==
                  //   'commissions'
                  // ) {
                  //   commissionRate = commissionRate.div(
                  //     statementCommissionRate || 100
                  //   );
                  // } else {
                  commissionRate = commissionRate.div(100);
                  // }
                  const adjustResult = await this.commissionUtils.adjustRate(
                    {
                      commissionRate,
                      agentApplicableProfile: agentCommissionProfile,
                    },
                    lookupData,
                    statement,
                    { account_id: req.account_id },
                    // @ts-expect-error
                    useCompGrids
                  );
                  commissionRate = adjustResult.commissionRate;
                  payoutRate = commissionRate.times(100);
                  commissionAmount = this.commissionUtils.alignSign(
                    commissionRate
                      .times(calculationBasisAmountSplitted)
                      .times(
                        BigNumber(
                          agentCommissionProfile?.agentProfileConfig
                            ?.multiplier ?? 100
                        ).div(100)
                      ),
                    commissionReceivedSign
                  );
                } else {
                  if (
                    agentCommissionProfile.agentProfileConfig
                      ?.agent_commission_schedule_profile?.calculation_basis !==
                    'commissions'
                  ) {
                    commissionRate = commissionRate.div(
                      statementCommissionRate || 100
                    );
                  } else {
                    commissionRate = commissionRate.div(100);
                  }
                  const adjustResult = await this.commissionUtils.adjustRate(
                    {
                      commissionRate,
                      agentApplicableProfile: agentCommissionProfile,
                    },
                    lookupData,
                    statement,
                    // @ts-expect-error
                    { account_id: req.account_id },
                    useCompGrids
                  );
                  commissionRate = adjustResult.commissionRate;
                  payoutRate = rule?.limit_to_received
                    ? BigNumber.min(
                        BigNumber(+rule.payout_rate),
                        BigNumber(statementCommissionRate || 100)
                      )
                    : BigNumber(+rule.payout_rate);
                  commissionAmount = this.commissionUtils.alignSign(
                    commissionRate
                      .times(commissionAmountSplitted)
                      .times(
                        BigNumber(
                          agentCommissionProfile?.agentProfileConfig
                            ?.multiplier ?? 100
                        ).div(100)
                      ),
                    commissionReceivedSign
                  );
                }

                runningTotal = runningTotal.plus(commissionAmount);

                const customResult =
                  await this.commissionCalculator.sequenceRunMethods({
                    agentApplicableProfile: agentCommissionProfile,
                    calculationBasis: calculationBasisAmountSplitted,
                    statement,
                    runningTotal,
                    // @ts-expect-error
                    contactsMap,
                    lookupData,
                    useCompGrids,
                    cache: ctx.profileCache,
                    ctx,
                    req,
                  });
                runningTotal = customResult?.runningTotal ?? runningTotal;

                ctx.addResult({
                  statementId: statement.id,
                  payeeStrId: payeeStrId,
                  amount: commissionAmount.toNumber(),
                  logs: {
                    ...curCalcLog,
                    calcBasis: actualCalcBasis || curCalcLog.calcBasis,
                    commissionAmount: commissionAmount.toNumber(),
                    payoutRate: payoutRate?.toNumber(),
                    commissionRate: commissionRate.toNumber(),
                  },
                });

                previousRate = previousRate.plus(commissionRate);
              } else if (rule.calculation_method === 'keepRate') {
                if (
                  [undefined, null].includes(statement.new_commission_rate) ||
                  Number.isNaN(+statement.new_commission_rate)
                ) {
                  // biome-ignore lint/suspicious/noConsole: Suppressed in initial biome linting check -- please fix these errors if you are modifying this file --INITIAL_FIX--
                  console.error('Error calculating keepRate');
                } else {
                  let commissionRate: BigNumber;
                  let payoutRate: BigNumber;
                  let compGridOverrideRate: BigNumber;
                  if (useCompGrids) {
                    const payerLevel =
                      agentCommissionProfile.agentProfileConfig
                        .agent_commission_schedule_profile
                        .payer_comp_grid_level;
                    const payeeLevel =
                      agentCommissionProfile.agentProfileConfig
                        .agent_commission_schedule_profile
                        .payee_comp_grid_level;
                    const criterionId =
                      agentCommissionProfile.rule.comp_grid_criteria_id?.[0];
                    // TODO: Only handling first criterion for now. Add check to ensure all are the same rate (they should be)
                    const payerRate = BigNumber(
                      payerLevel?.comp_grid_rates?.findLast(
                        // @ts-expect-error
                        (rate) => rate.comp_grid_criterion_id === criterionId
                      )?.rate ?? 0
                    );
                    const payeeRate = BigNumber(
                      payeeLevel?.comp_grid_rates?.findLast(
                        // @ts-expect-error
                        (rate) => rate.comp_grid_criterion_id === criterionId
                      )?.rate ?? 0
                    );
                    compGridOverrideRate = BigNumber.max(
                      0,
                      payerRate.minus(payeeRate)
                    );
                  }
                  if (rule.basis === 'commission_grid') {
                    const compGridRate = rule?.up_to_received_rate
                      ? BigNumber.max(
                          0,
                          // @ts-expect-error
                          compGridOverrideRate.minus(rule.keep_rate)
                        )
                      : // @ts-expect-error
                        compGridOverrideRate.minus(rule.keep_rate);
                    if (useCompGrids) {
                      commissionRate = compGridRate
                        .minus(
                          BigNumber(rule.plus_rate)
                            // @ts-expect-error
                            .times(compGridOverrideRate)
                            .div(100)
                        )
                        .div(statementCommissionRate);
                      const adjustResult =
                        await this.commissionUtils.adjustRate(
                          {
                            commissionRate,
                            agentApplicableProfile: agentCommissionProfile,
                          },
                          lookupData,
                          statement,
                          // @ts-expect-error
                          { account_id: req.account_id },
                          useCompGrids
                        );
                      commissionRate = adjustResult.commissionRate;
                      payoutRate = compGridRate.minus(
                        BigNumber(rule.plus_rate)
                          // @ts-expect-error
                          .times(compGridOverrideRate)
                          .div(100)
                      );

                      if (rule.percent_method === 'remainder') {
                        commissionRate = compGridRate
                          .times(BigNumber(100).minus(rule.plus_rate))
                          .div(statementCommissionRate)
                          .div(100);
                        const adjustResult =
                          await this.commissionUtils.adjustRate(
                            {
                              commissionRate,
                              agentApplicableProfile: agentCommissionProfile,
                            },
                            lookupData,
                            statement,
                            // @ts-expect-error
                            { account_id: req.account_id },
                            useCompGrids
                          );
                        commissionRate = adjustResult.commissionRate;
                        payoutRate = compGridRate
                          .minus(rule.keep_rate)
                          .times(BigNumber(100).minus(rule.plus_rate))
                          .div(100);
                      }
                    } else {
                      commissionRate = BigNumber(
                        rule.carrier_grid_override_rate
                      )
                        .minus(rule.keep_rate)
                        .minus(
                          BigNumber(rule.plus_rate)
                            .times(rule.carrier_grid_override_rate)
                            .div(100)
                        )
                        .div(statementCommissionRate);
                      const adjustResult =
                        await this.commissionUtils.adjustRate(
                          {
                            commissionRate,
                            agentApplicableProfile: agentCommissionProfile,
                          },
                          lookupData,
                          statement,
                          // @ts-expect-error
                          { account_id: req.account_id },
                          useCompGrids
                        );
                      commissionRate = adjustResult.commissionRate;
                      payoutRate = BigNumber(rule.carrier_grid_override_rate)
                        .minus(rule.keep_rate)
                        .minus(
                          BigNumber(rule.plus_rate)
                            .times(rule.carrier_grid_override_rate)
                            .div(100)
                        );
                      if (rule.percent_method === 'remainder') {
                        commissionRate = BigNumber(
                          rule.carrier_grid_override_rate
                        )
                          .minus(rule.keep_rate)
                          .times(BigNumber(100).minus(rule.plus_rate))
                          .div(statementCommissionRate)
                          .div(100);
                        const adjustResult =
                          await this.commissionUtils.adjustRate(
                            {
                              commissionRate,
                              agentApplicableProfile: agentCommissionProfile,
                            },
                            lookupData,
                            statement,
                            // @ts-expect-error
                            { account_id: req.account_id },
                            useCompGrids
                          );
                        commissionRate = adjustResult.commissionRate;
                        payoutRate = BigNumber(rule.carrier_grid_override_rate)
                          .minus(rule.keep_rate)
                          .times(BigNumber(100).minus(rule.plus_rate))
                          .div(100);
                      }
                    }
                  } else {
                    commissionRate = BigNumber(statement.new_commission_rate)
                      .minus(
                        Number.isNaN(+rule.keep_rate)
                          ? 0
                          : BigNumber(rule.keep_rate).div(100)
                      )
                      .minus(
                        Number.isNaN(+rule.plus_rate)
                          ? 0
                          : BigNumber(rule.plus_rate)
                              .div(100)
                              .times(statement.new_commission_rate)
                      )
                      .div(statement.new_commission_rate);
                    const adjustResult = await this.commissionUtils.adjustRate(
                      {
                        commissionRate,
                        agentApplicableProfile: agentCommissionProfile,
                      },
                      lookupData,
                      statement,
                      // @ts-expect-error
                      { account_id: req.account_id },
                      useCompGrids
                    );
                    commissionRate = adjustResult.commissionRate;
                    payoutRate = BigNumber(statement.new_commission_rate)
                      .minus(
                        Number.isNaN(+rule.keep_rate)
                          ? 0
                          : BigNumber(rule.keep_rate).div(100)
                      )
                      .minus(
                        Number.isNaN(+rule.plus_rate)
                          ? 0
                          : BigNumber(rule.plus_rate)
                              .div(100)
                              .times(statement.new_commission_rate)
                      );
                    // TODO: Check that all schedules have this configured, then
                    //   enforce that one or the other is specified if plus_rate is specified.
                    if (rule.percent_method === 'remainder') {
                      commissionRate = BigNumber(statement.new_commission_rate)
                        .minus(BigNumber(rule.keep_rate).div(100))
                        .times(BigNumber(100).minus(BigNumber(rule.plus_rate)))
                        .div(statementCommissionRate);
                      const adjustResult =
                        await this.commissionUtils.adjustRate(
                          {
                            commissionRate,
                            agentApplicableProfile: agentCommissionProfile,
                          },
                          lookupData,
                          statement,
                          // @ts-expect-error
                          { account_id: req.account_id },
                          useCompGrids
                        );
                      commissionRate = adjustResult.commissionRate;
                      payoutRate = BigNumber(statement.new_commission_rate)
                        .minus(BigNumber(rule.keep_rate).div(100))
                        .times(BigNumber(100).minus(BigNumber(rule.plus_rate)));
                    }
                  }
                  // TODO: Check this
                  // payoutRate = Math.max(payoutRate, 0);
                  let commissionAmount = this.commissionUtils.alignSign(
                    commissionRate.times(commissionAmountSplitted).times(
                      BigNumber(
                        // @ts-expect-error
                        numberOrDefault(
                          agentCommissionProfile?.agentProfileConfig
                            ?.multiplier,
                          100
                        )
                      ).div(100)
                    ),
                    commissionReceivedSign
                  );
                  if (
                    agentCommissionProfile.agentProfileConfig
                      ?.agent_commission_schedule_profile?.calculation_scope ===
                    'policy'
                  ) {
                    const result =
                      await this.commissionCalculator.policyScopeCalc({
                        accountId: req.account_id,
                        agentCommissionProfile,
                        calcBasis: calculationBasisAmountSplitted,
                        calcMethod: 'keepRate',
                        calculationParallelLevel,
                        commissionAmountBasis: commissionAmountSplitted,
                        contact: contactsMap[payeeStrId],
                        payoutRate: BigNumber(rule.keep_rate).div(100),
                        policyCommissionsSeenAmount:
                          ctx.policyCommissionsSeenAmount,
                        policyCommissionsSeenRecords:
                          ctx.policyCommissionsSeenRecords,
                        policyCommissionsUsedAmount:
                          ctx.policyCommissionsUsedAmount,
                        policyCommissionsUsedRecords:
                          ctx.policyCommissionsUsedAmount,
                        rule,
                        statement,
                      });
                    ({ commissionAmount, commissionRate, actualCalcBasis } =
                      result);
                    curCalcLog = {
                      ...curCalcLog,
                      policyCommissionsUsed: result.policyCommissionsUsed,
                      scope: 'Policy',
                    };
                  }

                  runningTotal = runningTotal.plus(commissionAmount);
                  const customResult =
                    await this.commissionCalculator.sequenceRunMethods({
                      agentApplicableProfile: agentCommissionProfile,
                      calculationBasis: calculationBasisAmountSplitted,
                      statement,
                      // @ts-expect-error
                      contactsMap,

                      lookupData,
                      useCompGrids,
                      cache: ctx.profileCache,
                      runningTotal,
                      ctx,
                      req,
                    });
                  runningTotal = customResult?.runningTotal ?? runningTotal;
                  ctx.agentCommissionCalc[statement.id] = {
                    ...(ctx.agentCommissionCalc[statement.id] ?? {}),
                    [payeeStrId]: commissionAmount.plus(
                      ctx.agentCommissionCalc[statement.id]?.[payeeStrId] ?? 0
                    ),
                    total: commissionAmount.plus(
                      ctx.agentCommissionCalc[statement.id]?.total ?? 0
                    ),
                  };
                  ctx.agentCommissionCalcLog[statement.id] = {
                    ...(ctx.agentCommissionCalcLog[statement.id] ?? {}),
                    [payeeStrId]: [
                      ...(ctx.agentCommissionCalcLog[statement.id]?.[
                        payeeStrId
                      ] ?? []),
                      {
                        ...curCalcLog,
                        // biome-ignore lint/complexity/useDateNow: Suppressed in initial biome linting check -- please fix these errors if you are modifying this file --INITIAL_FIX--
                        calculatedAt: new Date().getTime(),
                        calcBasis: actualCalcBasis || curCalcLog.calcBasis,
                        commissionRate: commissionRate,
                        commissionAmount: commissionAmount,
                        payoutRate: payoutRate,
                      },
                    ],
                  };
                  previousRate = previousRate.plus(commissionRate);
                  taskLogger.info(
                    `Found applicable profile using ${rule.calculation_method} method, calculating commission: (commission: ${statement.str_id}, agent: ${agentStrId}), commission_profile: ${agentCommissionProfile.agentProfileConfig.agent_commission_schedule_profile.str_id}, rule.commission_rate: ${agentCommissionProfile.rule.commission_rate}, rule.receivable_rate: ${agentCommissionProfile.rule.receivable_rate}, commissionRate: ${commissionRate}, commissionAmount: ${commissionAmount}`
                  );
                }
              } else if (rule.calculation_method === 'overrideSplit') {
                const payoutRate = BigNumber(rule.split);
                let commissionRate = BigNumber(rule.split).div(100);
                const adjustResult = await this.commissionUtils.adjustRate(
                  {
                    commissionRate,
                    agentApplicableProfile: agentCommissionProfile,
                  },
                  lookupData,
                  statement,
                  // @ts-expect-error
                  { account_id: req.account_id },
                  useCompGrids
                );
                commissionRate = adjustResult.commissionRate;
                const { finalCalcBasis, sign } =
                  this.commissionCalculator.getFinalCalcBasisWithSign({
                    defaultBasis: calculationBasisAmount,
                    agentSplit,
                    runningTotal,
                    calculationBasis:
                      agentCommissionProfile?.agentProfileConfig
                        ?.agent_commission_schedule_profile?.calculation_basis,
                    statement,
                  });
                const commissionAmount = this.commissionUtils.alignSign(
                  commissionRate
                    .times(finalCalcBasis)
                    .times(
                      BigNumber(
                        agentCommissionProfile?.agentProfileConfig
                          ?.multiplier ?? 100
                      ).div(100)
                    ),
                  sign
                );
                runningTotal = runningTotal.plus(commissionAmount);
                const customResult =
                  await this.commissionCalculator.sequenceRunMethods({
                    agentApplicableProfile: agentCommissionProfile,
                    calculationBasis: calculationBasisAmountSplitted,
                    statement,
                    // @ts-expect-error
                    contactsMap,
                    runningTotal,

                    lookupData,
                    useCompGrids,
                    cache: ctx.profileCache,
                    ctx,
                    req,
                  });
                runningTotal = customResult?.runningTotal ?? runningTotal;

                ctx.agentCommissionCalc[statement.id] = {
                  ...(ctx.agentCommissionCalc[statement.id] ?? {}),
                  [payeeStrId]: commissionAmount.plus(
                    ctx.agentCommissionCalc[statement.id]?.[payeeStrId] ?? 0
                  ),
                  total: commissionAmount.plus(
                    ctx.agentCommissionCalc[statement.id]?.total ?? 0
                  ),
                };
                ctx.agentCommissionCalcLog[statement.id] = {
                  ...(ctx.agentCommissionCalcLog[statement.id] ?? {}),
                  [payeeStrId]: [
                    ...(ctx.agentCommissionCalcLog[statement.id]?.[
                      payeeStrId
                    ] ?? []),
                    {
                      ...curCalcLog,
                      calcBasis: finalCalcBasis,
                      // biome-ignore lint/complexity/useDateNow: Suppressed in initial biome linting check -- please fix these errors if you are modifying this file --INITIAL_FIX--
                      calculatedAt: new Date().getTime(),
                      commissionRate: commissionRate,
                      commissionAmount: commissionAmount,
                      payoutRate: payoutRate,
                    },
                  ],
                };
                previousRate = previousRate.plus(commissionRate);
                taskLogger.info(
                  `Found applicable profile using ${rule.calculation_method} method, calculating commission: (commission: ${statement.str_id}, agent: ${agentStrId}), commission_profile: ${agentCommissionProfile.agentProfileConfig.agent_commission_schedule_profile.str_id}, rule.commission_rate: ${agentCommissionProfile.rule.commission_rate}, rule.receivable_rate: ${agentCommissionProfile.rule.receivable_rate}, commissionRate: ${commissionRate}, commissionAmount: ${commissionAmount}`
                );
              } else if (
                rule.calculation_method === 'carrierGridSplitRemainder'
              ) {
                // For now, agent in policy / commission will be the agency
                // Separately payout to the producer specified in the rule
                let payoutRate = BigNumber(rule.carrier_grid_override_rate);
                let commissionRate = BigNumber(rule.carrier_grid_override_rate)
                  .div(statementCommissionRate)
                  .times(BigNumber(rule.split).div(100));
                const adjustResult = await this.commissionUtils.adjustRate(
                  {
                    commissionRate,
                    agentApplicableProfile: agentCommissionProfile,
                  },
                  lookupData,
                  statement,
                  // @ts-expect-error
                  { account_id: req.account_id },
                  useCompGrids
                );
                commissionRate = adjustResult.commissionRate;
                if (useCompGrids) {
                  const payerLevel =
                    agentCommissionProfile.agentProfileConfig
                      .agent_commission_schedule_profile.payer_comp_grid_level;
                  const payeeLevel =
                    agentCommissionProfile.agentProfileConfig
                      .agent_commission_schedule_profile.payee_comp_grid_level;
                  const criterionId =
                    agentCommissionProfile.rule.comp_grid_criteria_id?.[0];
                  const payerRates = payerLevel?.comp_grid_rates?.findLast(
                    // @ts-expect-error
                    (rate) => rate.comp_grid_criterion_id === criterionId
                  );
                  const payeeRates = payeeLevel?.comp_grid_rates?.findLast(
                    // @ts-expect-error
                    (rate) => rate.comp_grid_criterion_id === criterionId
                  );
                  if (
                    this.commissionUtils.checkInvalidRate(payerRates, [
                      'rate',
                    ]) ||
                    this.commissionUtils.checkInvalidRate(payeeRates, ['rate'])
                  ) {
                    req.logger.error(
                      `The payer or payee grid rate is missing. criteriaId: ${criterionId}, level: ${payerLevel?.id}, date: ${effectiveDate?.toISOString()}`
                    );
                    ctx.addResult({
                      statementId: statement.id,
                      payeeStrId: payeeStrId,
                      amount: 0,
                      logs: {
                        ...curCalcLog,
                        commissionAmount: 0,
                        criterionId,
                        alerts:
                          'The compensation grid rate is missing (including null payer or payee rates, house rates, or carrier rates).',
                      },
                    });
                    continue;
                  }
                  // TODO: Only handling first criterion for now. Add check to ensure all are the same rate (they should be)
                  const payerRate = BigNumber(payerRates?.rate ?? 0);
                  const payeeRate = BigNumber(payeeRates?.rate ?? 0);
                  payoutRate = BigNumber.max(0, payerRate.minus(payeeRate));
                  commissionRate = payoutRate
                    .div(statementCommissionRate)
                    .times(BigNumber(rule.split).div(100));
                  const adjustResult = await this.commissionUtils.adjustRate(
                    {
                      commissionRate,
                      agentApplicableProfile: agentCommissionProfile,
                    },
                    lookupData,
                    statement,
                    // @ts-expect-error
                    { account_id: req.account_id },
                    useCompGrids
                  );
                  commissionRate = adjustResult.commissionRate;
                }
                const commissionAmount = this.commissionUtils.alignSign(
                  commissionRate
                    .times(commissionAmountSplitted)
                    .times(
                      BigNumber(
                        agentCommissionProfile?.agentProfileConfig
                          ?.multiplier ?? 100
                      ).div(100)
                    ),
                  commissionReceivedSign
                );
                runningTotal = runningTotal.plus(commissionAmount);
                const customResult =
                  await this.commissionCalculator.sequenceRunMethods({
                    agentApplicableProfile: agentCommissionProfile,
                    calculationBasis: calculationBasisAmountSplitted,
                    statement,
                    // @ts-expect-error
                    contactsMap,
                    runningTotal,

                    lookupData,
                    useCompGrids,
                    cache: ctx.profileCache,
                    ctx,
                    req,
                  });
                runningTotal = customResult?.runningTotal ?? runningTotal;
                ctx.agentCommissionCalc[statement.id] = {
                  ...(ctx.agentCommissionCalc[statement.id] ?? {}),
                  [payeeStrId]: commissionAmount.plus(
                    ctx.agentCommissionCalc[statement.id]?.[payeeStrId] ?? 0
                  ),
                  total: commissionAmount.plus(
                    ctx.agentCommissionCalc[statement.id]?.total ?? 0
                  ),
                };
                ctx.agentCommissionCalcLog[statement.id] = {
                  ...(ctx.agentCommissionCalcLog[statement.id] ?? {}),
                  [payeeStrId]: [
                    ...(ctx.agentCommissionCalcLog[statement.id]?.[
                      payeeStrId
                    ] ?? []),
                    {
                      ...curCalcLog,
                      // biome-ignore lint/complexity/useDateNow: Suppressed in initial biome linting check -- please fix these errors if you are modifying this file --INITIAL_FIX--
                      calculatedAt: new Date().getTime(),
                      commissionRate: commissionRate,
                      commissionAmount: commissionAmount,
                      payoutRate: payoutRate,
                    },
                  ],
                };
                if (rule.agent) {
                  const producer = contactsMap[+rule.agent];
                  if (producer) {
                    const payoutRate = BigNumber(statementCommissionRate).minus(
                      rule.carrier_grid_override_rate
                    );
                    const producerCommissionRate = BigNumber(
                      statementCommissionRate
                    )
                      .minus(rule.carrier_grid_override_rate)
                      .div(statementCommissionRate);
                    const producerCommissionAmount =
                      this.commissionUtils.alignSign(
                        producerCommissionRate
                          .times(commissionAmountSplitted)
                          .times(
                            BigNumber(
                              // @ts-expect-error
                              numberOrDefault(
                                agentCommissionProfile?.agentProfileConfig
                                  ?.multiplier,
                                100
                              )
                            ).div(100)
                          ),
                        commissionReceivedSign
                      );
                    runningTotal = BigNumber(producerCommissionAmount);
                    ctx.agentCommissionCalc[statement.id] = {
                      ...(ctx.agentCommissionCalc[statement.id] ?? {}),
                      [producer.str_id ?? 'n/a']: producerCommissionAmount.plus(
                        ctx.agentCommissionCalc[statement.id]?.[
                          producer.str_id ?? 'n/a'
                        ] ?? 0
                      ),
                      total: producerCommissionAmount.plus(
                        ctx.agentCommissionCalc[statement.id]?.total ?? 0
                      ),
                    };
                    ctx.agentCommissionCalcLog[statement.id] = {
                      ...(ctx.agentCommissionCalcLog[statement.id] ?? {}),
                      [payeeStrId]: [
                        ...(ctx.agentCommissionCalcLog[statement.id]?.[
                          payeeStrId
                        ] ?? []),
                        {
                          ...curCalcLog,
                          // biome-ignore lint/complexity/useDateNow: Suppressed in initial biome linting check -- please fix these errors if you are modifying this file --INITIAL_FIX--
                          calculatedAt: new Date().getTime(),
                          commissionRate: commissionRate,
                          commissionAmount: commissionAmount,
                          payoutRate: payoutRate,
                        },
                      ],
                    };
                  }
                }
              } else if (
                rule.calculation_method === 'payoutRateIncentiveTiers'
              ) {
                const tiersIdsRates = Object.entries(rule)
                  .filter(([key]) => key.startsWith('incentive_tier::'))
                  .reduce((acc, [key, value]) => {
                    const tierNumber = key.split('::')[1];
                    // @ts-expect-error
                    acc[tierNumber] = value;
                    return acc;
                  }, {});

                const tiers = await prismaClient.agent_incentive_tiers.findMany(
                  {
                    where: {
                      id: { in: Object.keys(tiersIdsRates).map(Number) },
                      state: 'active',
                      account_id: req.account_id,
                    },
                    include: {
                      companies: {
                        where: { state: 'active' },
                        select: { id: true },
                      },
                    },
                  }
                );

                const applicableTiers = tiers.filter((tier) => {
                  // Do we need to filter by company as well? Incentive tiers
                  // should only be available to applicable companies, so skipping check.
                  const datesMatch =
                    !effectiveDate ||
                    // @ts-expect-error
                    (tier.start_date <= effectiveDate &&
                      (!tier.end_date || tier.end_date >= effectiveDate));
                  let incentiveBasis = 0;
                  switch (tier.calculation_basis) {
                    case 'policy_annual_premium':
                      incentiveBasis = statement.report?.premium_amount ?? 0;
                      break;
                    case 'policy_target_premium':
                      incentiveBasis =
                        statement.report?.commissionable_premium_amount ?? 0;
                      break;
                    case 'commission_annual_premium':
                      incentiveBasis = statement.premium_amount ?? 0;
                      break;
                    case 'commission_target_premium':
                      incentiveBasis =
                        statement.commissionable_premium_amount ?? 0;
                      break;
                    case 'commission_amount':
                      incentiveBasis = statement.commission_amount ?? 0;
                      break;
                    default:
                      incentiveBasis = 0;
                  }
                  const thresholdMatch =
                    (!tier.threshold_min ||
                      +tier.threshold_min <= +incentiveBasis) &&
                    (!tier.threshold_max ||
                      +tier.threshold_max > +incentiveBasis);
                  return datesMatch && thresholdMatch;
                });

                if (applicableTiers.length === 0) {
                  // biome-ignore lint/suspicious/noConsole: Suppressed in initial biome linting check -- please fix these errors if you are modifying this file --INITIAL_FIX--
                  console.log(
                    `No incentive tiers found for statement ${statement.str_id}`
                  );
                  continue;
                }

                const tier = applicableTiers[0];
                // @ts-expect-error
                const tierRate = tiersIdsRates[tier.id.toString()];
                let commissionRate = BigNumber(tierRate)
                  .div(100)
                  .times(
                    BigNumber(
                      +agentCommissionProfile?.agentProfileConfig?.multiplier ||
                        100
                    )
                  )
                  .div(100);
                const adjustResult = await this.commissionUtils.adjustRate(
                  {
                    commissionRate,
                    agentApplicableProfile: agentCommissionProfile,
                  },
                  lookupData,
                  statement,
                  // @ts-expect-error
                  { account_id: req.account_id },
                  useCompGrids
                );
                commissionRate = adjustResult.commissionRate;

                let incentiveBasis = 0;
                switch (tier.calculation_basis) {
                  case 'policy_annual_premium':
                    incentiveBasis = statement.report?.premium_amount ?? 0;
                    break;
                  case 'policy_target_premium':
                    incentiveBasis =
                      statement.report?.commissionable_premium_amount ?? 0;
                    break;
                  case 'commission_annual_premium':
                    incentiveBasis = statement.premium_amount ?? 0;
                    break;
                  case 'commission_target_premium':
                    incentiveBasis =
                      statement.commissionable_premium_amount ?? 0;
                    break;
                  case 'commission_amount':
                    incentiveBasis = statement.commission_amount ?? 0;
                    break;
                  default:
                    incentiveBasis = 0;
                }

                const commissionAmount = this.commissionUtils.alignSign(
                  commissionRate
                    .times(new BigNumber(incentiveBasis))
                    .times(
                      BigNumber(
                        agentCommissionProfile?.agentProfileConfig
                          ?.multiplier ?? 100
                      ).div(100)
                    ),
                  commissionReceivedSign
                );

                // Insert negative of previous commission record
                // TODO: For demo purposes only. In future, we need proper filtering to identify correct one
                const previousCommissions =
                  await prismaClient.statement_data.findMany({
                    where: {
                      account_id: req.account_id,
                      contacts: { has: payeeStrId },
                      agent_commissions: {
                        path: [payeeStrId],
                        // @ts-expect-error
                        not: null,
                      },
                      id: { lt: statement.id },
                    },
                    orderBy: [{ id: 'desc' }],
                  });

                let previousCommissionAmount = 0;
                // biome-ignore lint/complexity/noForEach: Suppressed in initial biome linting check -- please fix these errors if you are modifying this file --INITIAL_FIX--
                previousCommissions.forEach(
                  (previousCommission) =>
                    // biome-ignore lint/suspicious/noAssignInExpressions: Suppressed in initial biome linting check -- please fix these errors if you are modifying this file --INITIAL_FIX--
                    (previousCommissionAmount +=
                      // @ts-expect-error
                      previousCommission?.agent_commissions?.[payeeStrId] ?? 0)
                );

                if (previousCommissionAmount) {
                  ctx.addResult({
                    statementId: statement.id,
                    payeeStrId: payeeStrId,
                    amount: -+previousCommissionAmount,
                    logs: {
                      ...curCalcLog,
                      // biome-ignore lint/complexity/useDateNow: Suppressed in initial biome linting check -- please fix these errors if you are modifying this file --INITIAL_FIX--
                      calculatedAt: new Date().getTime(),
                      calcBasis: incentiveBasis,
                      commissionAmount: -+previousCommissionAmount,
                      notes: `Paid amount`,
                    },
                  });
                }

                ctx.addResult({
                  statementId: statement.id,
                  payeeStrId: payeeStrId,
                  amount: commissionAmount.toNumber(),
                  logs: {
                    ...curCalcLog,
                    alerts:
                      applicableTiers.length > 1
                        ? 'Multiple tiers matched'
                        : undefined,
                    // biome-ignore lint/complexity/useDateNow: Suppressed in initial biome linting check -- please fix these errors if you are modifying this file --INITIAL_FIX--
                    calculatedAt: new Date().getTime(),
                    calcBasis: incentiveBasis,
                    payoutRate: tierRate,
                    commissionAmount: commissionAmount,
                    notes: `Tier: ${tier.name}`,
                  },
                });
              }
            }
            commissionsUsed = commissionsUsed.plus(runningTotal);
          }

          if (contactsMap[statementContactStrId]?.contact_referrals?.length) {
            const agentCompProfile =
              ancestryApplicableProfiles?.[0]?.agentCommissionProfile;
            const criterionId = agentCompProfile?.matchedCriteriaId;
            const agentCompGridId =
              agentCompProfile?.agentProfileConfig
                ?.agent_commission_schedule_profile?.comp_grid_id ?? -1;
            const compGrid = await prismaClient.comp_grids.findUnique({
              where: {
                id: agentCompGridId,
                account_id: req.account_id,
                state: 'active',
              },
              include: {
                comp_grid_levels: {
                  where: {
                    OR: [{ name: 'S' }, { name: 'A3' }],
                  },
                  include: {
                    comp_grid_rates: {
                      where: {
                        comp_grid_criterion_id: criterionId,
                        ...(effectiveDate && {
                          date_ranges: {
                            some: {
                              start_date: {
                                lte: effectiveDate,
                              },
                              end_date: {
                                gte: effectiveDate,
                              },
                            },
                          },
                        }),
                      },
                    },
                  },
                },
              },
            });

            const sRate = compGrid?.comp_grid_levels?.find(
              (level) => level.name === 'S'
            )?.comp_grid_rates?.[0]?.rate;
            const a3Rate = compGrid?.comp_grid_levels?.find(
              (level) => level.name === 'A3'
            )?.comp_grid_rates?.[0]?.rate;

            const isAnnuity = this.commissionUtils.isAnnuity(
              statement,
              agentCompProfile?.matchedCriteriaStr?.toLowerCase()
            );
            const referralResults = this.commissionCalculator.referralCalc({
              agentCommissionProfile: agentCompProfile,
              calculationBasisAmountSplitted,
              statement,
              statementContactStrId,
              contactsMap,
              isAnnuity,
              sRate,
              a3Rate,
              effectiveDate,
            });
            // biome-ignore lint/complexity/noForEach: Suppressed in initial biome linting check -- please fix these errors if you are modifying this file --INITIAL_FIX--
            referralResults.forEach((result) => {
              ctx.addResult({
                ...result,
                statementId: result.key,
                payeeStrId: result.subKey,
              });
            });
          }
        }

        if (statement.hasGroupedCommissions) {
          if (
            // biome-ignore lint/complexity/useOptionalChain: Suppressed in initial biome linting check -- please fix these errors if you are modifying this file --INITIAL_FIX--
            ctx.agentCommissionCalcLog &&
            ctx.agentCommissionCalcLog[statement.id]
          ) {
            const agentLog = ctx.agentCommissionCalcLog[statement.id];
            // biome-ignore lint/complexity/noForEach: Suppressed in initial biome linting check -- please fix these errors if you are modifying this file --INITIAL_FIX--
            Object.keys(agentLog).forEach((agentStrId) => {
              const logItems = agentLog[agentStrId];
              if (Array.isArray(logItems)) {
                // biome-ignore lint/complexity/noForEach: Suppressed in initial biome linting check -- please fix these errors if you are modifying this file --INITIAL_FIX--
                logItems.forEach((item) => {
                  // @ts-expect-error
                  // biome-ignore lint/complexity/useLiteralKeys: Suppressed in initial biome linting check -- please fix these errors if you are modifying this file --INITIAL_FIX--
                  item['commissionGroupUsed'] = `🔗`;
                });
              }
            });
          }
        }

        const endStatementTime = Date.now();
        const durationStatementTime = endStatementTime - startStatementTime;
        if (ctx.agentCommissionCalc[statement.id]) {
          taskLogger.info(
            `agentCommissionCalc[${statement.id}]: ${JSON.stringify(ctx.agentCommissionCalc[statement.id])} (${durationStatementTime}ms)`
          );
        } else {
          taskLogger.info(
            `No commission profiles applicable for statement data: ${statement.id} (${durationStatementTime}ms)`
          );
        }
        taskLogger.profile('taskHandlerNew');
        processedCountNew++;
      };

      // const tasks = chunk(
      //   statementData,
      //   +process.env.COMMISSION_CHUNK_SIZE || 50
      // );
      // for (let i = 0; i < tasks.length; i++) {
      //   const title = `Batch processing task ${i} `;
      //   console.time(title);
      //   await Promise.all(
      //     tasks[i].map((item) =>
      //       pRetry(() => taskHandler(item), {
      //         minTimeout: 100,
      //         retries: 3,
      //       }).catch((err) => {
      //         failedTasks.push(item.id);
      //         console.error(
      //           `Failed to process statement data ${item.id}: ${err}`
      //         );
      //       })
      //     )
      //   );
      //   console.timeEnd(title);
      // }

      // @ts-expect-error
      const onFail = (context: { error; data: statement_data }) => {
        // biome-ignore lint/suspicious/noConsole: Suppressed in initial biome linting check -- please fix these errors if you are modifying this file --INITIAL_FIX--
        console.error(context.error);
        ctx.failedTasks.push(context.data.id);
        if (
          context.error instanceof UnsupportedCalculationParallelLevelException
        ) {
          errorOfPolicyScopeLevel = true;
        }
      };

      await limitConcurrency(
        taskHandlerNew,
        statementData,
        calculationParallelLevel,
        { onFail, retries: 0 }
      );

      // Throw error to prevent writing calc results if policy scope with wrong parallel level
      if (errorOfPolicyScopeLevel) {
        throw new UnsupportedCalculationParallelLevelException();
      }

      if (onlyGetProfilesRates) {
        return ctx.agentCommissionCalcLog;
      }

      req.logger.info('agentCommissionCalc', { ...ctx.agentCommissionCalc });
      req.logger.info('agentCommissionCalcLog', {
        ...ctx.agentCommissionCalcLog,
      });

      req.logger.info(`cacheHits: ${cacheHits}`);
      const results = ctx.agentCommissionCalc;

      if (ctx.regressionTestMode) {
        const total = statementData.length;
        let matched = 0;
        const diff = statementData
          .filter((data) => {
            // Convert all value of ctx.agentCommissionCalc to number, since it might be BigNumber instance
            const actual = this.commissionUtils.standardizeCommissionResult(
              ctx.agentCommissionCalc[data.id]
            );
            const expected = this.commissionUtils.standardizeCommissionResult(
              data.agent_commissions as { [key: string]: number | BigNumber }
            );
            const isMatched = isEqual(actual, expected);
            if (isMatched) {
              matched++;
            }
            return !isMatched;
          })
          .map((r) => ({
            id: r.id,
            str_id: r.str_id,
            expected: r.agent_commissions,
            actual: ctx.agentCommissionCalc[r.id],
          }));
        return {
          diff,
          total,
          matched,
          logs: ctx.agentCommissionCalcLog,
          results: results,
        };
      }
      // Clear out previous results for rows being processed
      const statementDataIds = statementData.map((s) => s.id);
      await prismaClient.statement_data.updateMany({
        where: {
          account_id: req.account_id,
          id: { in: statementDataIds },
          OR: COMMISSION_STATUS_EDITABLE,
        },
        data: {
          agent_commissions: Prisma.DbNull,
          agent_commissions_v2: Prisma.DbNull,
          agent_commissions_log: Prisma.DbNull,
          agent_commissions_status: Prisma.DbNull,
          agent_commissions_status2: Prisma.DbNull,
          agent_payout_rate: Prisma.DbNull,
          agent_commission_payout_rate: Prisma.DbNull,
        },
      });
      await prismaClient.accounting_transaction_details.updateMany({
        where: {
          account_id: req.account_id,
          statement_data: {
            id: { in: statementDataIds },
            OR: COMMISSION_STATUS_EDITABLE,
          },
          state: 'active',
        },
        data: {
          state: 'deleted',
        },
      });

      // If no filter, clear out rows with no agents, since they're not included above
      if (
        !contactIds &&
        !documentIds &&
        !endDate &&
        !id &&
        !payingEntity &&
        !policyId &&
        !startDate
      ) {
        await prismaClient.statement_data.updateMany({
          where: {
            account_id: req.account_id,
            contacts: { isEmpty: true },
            OR: COMMISSION_STATUS_EDITABLE,
          },
          data: {
            agent_commissions_v2: Prisma.DbNull,
            agent_commissions: Prisma.DbNull,
            agent_commissions_log: Prisma.DbNull,
            agent_commissions_status: Prisma.DbNull,
            agent_commissions_status2: Prisma.DbNull,
            agent_payout_rate: Prisma.DbNull,
            agent_commission_payout_rate: Prisma.DbNull,
          },
        });
        await prismaClient.accounting_transaction_details.updateMany({
          where: {
            account_id: req.account_id,
            statement_data: {
              contacts: { isEmpty: true },
              OR: COMMISSION_STATUS_EDITABLE,
            },
            state: 'active',
          },
          data: {
            state: 'deleted',
          },
        });
      }

      await limitConcurrency(
        async ([statementId, commissions]) => {
          const {
            // @ts-expect-error
            agentPayoutRate: _agentPayoutRate,
            // @ts-expect-error
            agentCommissionPayoutRate,
          } = generateAgentPayoutRate(
            // @ts-expect-error
            lookupData.statements[statementId],
            commissions
          );
          let agentPayoutRate = _agentPayoutRate;
          // WorldChangers has a different payout rate for agents
          if (req.account_id === AccountIds.WORLD_CHANGERS) {
            const calcLog = ctx.agentCommissionCalcLog[statementId];
            agentPayoutRate = Object.fromEntries(
              // biome-ignore lint/correctness/noUnusedFunctionParameters: Suppressed in initial biome linting check -- please fix these errors if you are modifying this file --INITIAL_FIX--
              Object.entries(agentPayoutRate).map(([k, v]) => {
                const premiumAmount = BigNumber(
                  // @ts-expect-error
                  +lookupData.statements[statementId].premium_amount
                );
                const agentLogs = calcLog?.[k] ?? [];
                const agentRates = agentLogs.map((agentLog) => {
                  const agentCommissions = BigNumber(
                    agentLog.commissionAmount ?? 0
                  );
                  const agentRate = BigNumber(
                    // @ts-expect-error
                    numberOrDefault(
                      agentLog?.agentRates?.carrier_rate,
                      MATH_MAGIC_NUMBERS.PERCENTAGE_IDENTITY
                    )
                  ).div(MATH_MAGIC_NUMBERS.PERCENTAGE_IDENTITY);
                  const multiplier = agentLog?.multiplier ?? 1;
                  // TODO: Agent payout rate is still a work in progress, may need to figure out how to weight based on split
                  // But don't currently have any examples of non 50/50 splits. Leaving for future reference when we get cases.
                  const agentSplit = BigNumber(
                    agentLog?.agentSplit ?? MATH_MAGIC_NUMBERS.MULTIPLY_IDENTITY
                  );
                  const newRate = agentCommissions
                    .div(multiplier)
                    .div(premiumAmount)
                    .div(agentRate)
                    .div(agentSplit)
                    .times(MATH_MAGIC_NUMBERS.PERCENTAGE_IDENTITY);
                  return newRate;
                });
                const newRate =
                  agentRates.length === 0
                    ? BigNumber(0)
                    : agentRates
                        .reduce(
                          (acc, newRate) => acc.plus(newRate),
                          BigNumber(0)
                        )
                        .div(agentRates.length);
                return [k, newRate.toNumber().toString()];
              })
            );
          }
          return await prismaClient.statement_data.update({
            where: {
              id: +statementId,
            },
            data: {
              agent_commissions: commissions
                ? (Object.fromEntries(
                    Object.entries(commissions).map(([k, v]) => [
                      k,
                      BigNumber.isBigNumber(v) ? v.toNumber() : v,
                    ])
                  ) as Prisma.InputJsonValue)
                : undefined,
              // AgentCommissionCalcLog is a parallel object to agentCommissionCalc
              agent_commissions_log: ctx.agentCommissionCalcLog[statementId],
              accounting_transaction_details: commissions
                ? {
                    create: Object.entries(commissions)
                      // biome-ignore lint/correctness/noUnusedFunctionParameters: Suppressed in initial biome linting check -- please fix these errors if you are modifying this file --INITIAL_FIX--
                      .filter(([k, v]) => k !== 'total')
                      .map(([k, v]) => ({
                        account_id: req.account_id,
                        created_by: req.uid,
                        created_proxied_by: req.ouid,
                        // @ts-expect-error
                        amount: +v,
                        contact_id: contactsMap[k]?.id,
                        logs: ctx.agentCommissionCalcLog[statementId][k],
                        status: TransactionStatuses.DRAFT,
                        type: 'payable',
                      })),
                  }
                : undefined,
              agent_payout_rate: agentPayoutRate,
              agent_commission_payout_rate: agentCommissionPayoutRate,
              flags: ctx.flags[statementId],
            },
          });
        },
        Array.from(
          // biome-ignore lint/suspicious/noExplicitAny: Suppressed in initial biome linting check -- please fix these errors if you are modifying this file --INITIAL_FIX--
          new Map<string, any>([
            ...Object.keys(ctx.agentCommissionCalcLog).map(
              // biome-ignore lint/suspicious/noExplicitAny: Suppressed in initial biome linting check -- please fix these errors if you are modifying this file --INITIAL_FIX--
              (k) => [k, undefined] as [string, any]
            ),
            ...Object.entries(ctx.agentCommissionCalc),
          ])
        ),
        100
      );

      await this.commissionUtils.updateReferralTags(ctx, req, prismaClient);

      const endTime = Date.now();
      const hasErrors = ctx.failedTasks.length > 0;
      dataProcessing = await prismaClient.data_processing.update({
        // @ts-expect-error
        where: { id: dataProcessing.id },
        data: {
          status: hasErrors
            ? DataProcessingStatuses.ERROR
            : DataProcessingStatuses.COMPLETED,
          output: results,
          duration: endTime - startTime,
          stats: {
            totalCount: statementData.length,
            resultsCount: Object.keys(ctx.agentCommissionCalc).length,
            ...(hasErrors ? { failed: ctx.failedTasks } : {}),
          },
        },
      });

      // For task workers, need to return {success: true} when task is finished, which is handled in the QueueTaskHandler
      if (ctx.isWorkerJob) {
        return;
      }
      if (hasErrors) {
        // biome-ignore lint/suspicious/noConsole: Suppressed in initial biome linting check -- please fix these errors if you are modifying this file --INITIAL_FIX--
        console.error(
          'Error calculating agent commissions',
          JSON.stringify(ctx.failedTasks)
        );
        res.status(500).json({ data: results, statusText: 'error' });
      } else {
        res.status(200).json({ data: results, statusText: 'ok' });
      }
      return;
    } catch (error) {
      req.logger.error(
        // @ts-expect-error
        `Error calculating agent commissions: ${error.message}`,
        // @ts-expect-error
        error
      );
      Sentry.captureException(error);
      const endTime = Date.now();
      if (dataProcessing?.id) {
        dataProcessing = await prismaClient.data_processing.update({
          where: { id: dataProcessing.id },
          data: {
            status: DataProcessingStatuses.ERROR,
            stats: {
              failed: ctx.failedTasks,
              resultsCount: Object.keys(ctx.agentCommissionCalc).length,
            },
            duration: endTime - startTime,
          },
        });
      }
      if (ctx.isWorkerJob) {
        return;
      }
      throw error;
    } finally {
      ctx.profileCache?.clear();
      if (req.body.master_str_id) {
        await pRetry(
          async () =>
            prismaTransactionHandler(prismaClient, async (client) => {
              const master = await client.data_processing.findFirst({
                where: { str_id: req.body.master_str_id },
              });
              const verionControl = {
                success: master.success,
                failed: master.failed,
              };
              const progress = {
                total: master.total ?? 0,
                success:
                  (master?.success ?? 0) +
                  req.body.statementIds.length -
                  ctx.failedTasks.length,
                failed: (master?.failed ?? 0) + ctx.failedTasks.length,
              };
              let status = master.status;
              const stats = (master.stats || {}) as {
                failed: number[];
                resultsCount: number;
              };
              stats.resultsCount =
                (stats?.resultsCount ?? 0) +
                Object.keys(ctx.agentCommissionCalc).length;

              if (progress.success + progress.failed === progress.total) {
                stats.failed = [...(stats.failed ?? []), ...ctx.failedTasks];
                status = stats.failed?.length
                  ? DataProcessingStatuses.ERROR
                  : DataProcessingStatuses.COMPLETED;
              }
              const result = await client.data_processing.updateMany({
                where: { id: master.id, ...verionControl },
                data: {
                  status: status,
                  ...progress,
                  stats,
                  duration: Date.now() - master.created_at.getTime(),
                },
              });
              if (result.count === 0) {
                throw new Error('Concurrent update failed, retrying...');
              }
            }),
          { retries: 3, minTimeout: 100 }
        ).catch((err) => {
          req.logger.error(err);
          throw err;
        });
      }
      console.timeEnd('calculateAgentCommissions()');
      req.logger.info(this.timerStats.logString());
    }
  };

  deleteResults = async (req: ExtNextApiRequest, res: ExtNextApiResponse) => {
    const startTime = Date.now();
    // biome-ignore lint/suspicious/noImplicitAnyLet: Suppressed in initial biome linting check -- please fix these errors if you are modifying this file --INITIAL_FIX--
    let dataProcessing;
    try {
      // @ts-expect-error
      dataProcessing = await prismaClient.data_processing.create({
        data: {
          str_id: nanoid(),
          account: { connect: { str_id: req.account_id } },
          user: { connect: { uid: req.uid } },
          proxy_user: req.ouid ? { connect: { uid: req.ouid } } : undefined,
          type: DataProcessingTypes.agent_commission_calc,
          params: 'delete',
          status: DataProcessingStatuses.PROCESSING,
        },
      });
      const updatedCount = await prismaClient.statement_data.updateMany({
        where: {
          account_id: req.account_id,
          state: { in: [DataStates.ACTIVE, DataStates.GROUPED] },
          OR: COMMISSION_STATUS_EDITABLE,
        },
        data: {
          agent_commissions: Prisma.DbNull,
          agent_commissions_log: Prisma.DbNull,
          agent_commissions_status: Prisma.DbNull,
          agent_commissions_status2: Prisma.DbNull,
          agent_payout_rate: Prisma.DbNull,
          agent_commission_payout_rate: Prisma.DbNull,
        },
      });
      const endTime = Date.now();
      dataProcessing = await prismaClient.data_processing.update({
        where: { id: dataProcessing.id },
        data: {
          status: DataProcessingStatuses.COMPLETED,
          duration: endTime - startTime,
          stats: updatedCount,
        },
      });
      res.status(200).json({ data: dataProcessing, statusText: 'ok' });
    } catch (error) {
      // biome-ignore lint/suspicious/noConsole: Suppressed in initial biome linting check -- please fix these errors if you are modifying this file --INITIAL_FIX--
      console.error('Error deleting agent commissions', error);
      Sentry.captureException(error);
      // @ts-expect-error
      res.status(500).json({ error: error.message });
    }
  };
}
